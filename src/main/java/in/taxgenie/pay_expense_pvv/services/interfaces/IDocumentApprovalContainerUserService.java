package in.taxgenie.pay_expense_pvv.services.interfaces;

import com.taxgenie.cem.viewmodels.employee.keyvaluepair.EmployeeAllValuesForKeyViewModel;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.DocumentApprovalContainer;
import in.taxgenie.pay_expense_pvv.viewmodels.*;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.InvoicePreviewViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.POPreviewViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.po.PurchaseOrderContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PRPreviewViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.ProcurementViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.PurchaseRequestContainerViewModel;

import java.util.List;

public interface IDocumentApprovalContainerUserService {
    DocumentApprovalContainerViewModel create(long metadataId, IAuthContextViewModel auth);
    DocumentApprovalContainerViewModel getById(long id, IAuthContextViewModel auth);
    void save(DocumentApprovalContainerUpdateViewModel viewModel, IAuthContextViewModel auth);
    DocumentApprovalContainerSubmitResultViewModel submit(long id, IAuthContextViewModel auth, long saveDate, OtherDepartmentKeyValuePairsViewModel otherDepartmentKeyValuePairsViewModel);
    void revoke(long id, IAuthContextViewModel auth);
    void convertFailedToDraft(long reportId, IAuthContextViewModel auth);
    List<DocumentApprovalContainerViewModel> getQueue(IAuthContextViewModel auth);
	GenericPageableViewModel getQueueV2(IAuthContextViewModel auth, QueueFilterViewModel viewModel);
    GenericPageableViewModel<InvoiceContainerViewModel> getInvoiceQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues);
    InvoicePreviewViewModel getInvoiceQueueById(IAuthContextViewModel auth, long id);
    EmployeeAllValuesForKeyViewModel getKeyValuesByMetadataIdAndAmount(IAuthContextViewModel auth, ApprovalKeyValuePairsViewModel approvalMatrixGetViewModel);

    GenericPageableViewModel<PurchaseRequestContainerViewModel> getPurchaseRequestQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues);
    GenericPageableViewModel<BudgetContainerViewModel> getBudgetQueue(Long budgetStructureMasterId, IAuthContextViewModel auth, QueueFilterViewModel filterValues);
    GenericPageableViewModel<PurchaseOrderContainerViewModel> getPurchaseOrderQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues);
    POPreviewViewModel getPurchaseOrderQueueById(IAuthContextViewModel auth, long id);

    GenericPageableViewModel<ProcurementViewModel> getPurchaseRequestProcurementQueue(IAuthContextViewModel auth, QueueFilterViewModel filterValues);
    void markAsDraft(DocumentApprovalContainer documentApprovalContainer);
}
