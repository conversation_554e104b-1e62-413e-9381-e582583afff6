package in.taxgenie.pay_expense_pvv.services.interfaces;

import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.Document;
import in.taxgenie.pay_expense_pvv.entities.InvoiceHeader;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.govrndto.InvoiceDeatilsByIrnDTO;
import in.taxgenie.pay_expense_pvv.invoice.request.InvoiceDetailsReq;
import in.taxgenie.pay_expense_pvv.viewmodels.InvoiceApprovalContainerFromIRNViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.*;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.BuyerDocIdRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.StatusUpdateViewModel;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IInvoiceService {

    InvoiceApprovalContainerFromIRNViewModel getByIRN(MultipartFile invoice, IAuthContextViewModel auth);
    InvoiceDetailsReq getById(Long invoiceId, IAuthContextViewModel auth);

    DocumentCreateResponse create(long metadataId, long subgroupId, String invoiceDetailsRequest, List<MultipartFile> invoice, IAuthContextViewModel auth);

    InvoiceDetailsReq getByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth);

    void delete(long invoiceId, IAuthContextViewModel auth);

    boolean save(InvoiceDetailsReq invoiceDetailsRequest, IAuthContextViewModel auth);

    boolean saveInvoiceOCRJson(InvoiceDeatilsByIrnDTO invoiceDeatilsByIrnDTO, IAuthContextViewModel auth);

    boolean saveInvoiceBuyerDocId(BuyerDocIdRequestViewModel buyerDocIdRequestViewModel, IAuthContextViewModel auth);

    boolean saveInvoiceBuyerDocIdStatusUpdate(StatusUpdateViewModel statusUpdateViewModel, IAuthContextViewModel auth);

    void deleteLineItem(long invoiceId, List<Long> lineItemId, IAuthContextViewModel auth);

    InvoiceLineItemViewModel getByInvoiceId(Long invoiceId, IAuthContextViewModel auth);

    boolean checkExistenceByInvoiceNo(long supplierId, String invoiceNo, Long id, IAuthContextViewModel auth);

    InvoiceItemDetailsViewModel invoiceItemToDto(InvoiceItem invoiceItem, IAuthContextViewModel auth);
    InvoiceHeader getInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(long companyCode, Document document);
    Document getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(long companyCode, Long documentContainerId);

    String getInvoicePDFBase64ByDocumentIdentifier(String documentIdentifier, IAuthContextViewModel auth);

    SignedUrlResponseViewModel createSignedUrl(GetSignedUrlRequestViewModel getSignedUrlRequestViewModel, IAuthContextViewModel auth);
    BulkInvoiceUploadResponseViewModel invoiceBulkUpload(BulkInvoiceUploadRequestViewModel requestViewModel, IAuthContextViewModel auth);

    DocumentCreateResponse createInvoicePostUpload(QueueBulkInvoiceUploadDataViewModel invoiceDetailsRequest, IAuthContextViewModel auth);

    InvoicePoMatchResponse matchInvoiceWithPurchaseOrders(InvoicePoMatchRequest request, IAuthContextViewModel auth);
}
