package in.taxgenie.pay_expense_pvv.services.implementations.consumption;

import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.entities.pr.PurchaseRequestHeader;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.repositories.IDocumentRepository;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.pr.IPurchaseRequestHeaderRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.consumption.IConsumptionService;
import org.checkerframework.checker.units.qual.K;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ConsumptionServiceImplementation implements IConsumptionService {

    private final IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository;
    private final IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository;
    private final IDocumentRepository documentRepository;
    private final Logger logger;

    public ConsumptionServiceImplementation(IPurchaseRequestHeaderRepository purchaseRequestHeaderRepository, IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository, IDocumentRepository documentRepository) {
        this.purchaseRequestHeaderRepository = purchaseRequestHeaderRepository;
        this.purchaseOrderHeaderRepository = purchaseOrderHeaderRepository;
        this.documentRepository = documentRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void handleConsumption(DocumentApprovalContainer report) {
        DocumentType documentType = DocumentType.values()[report.getDocumentMetadata().getDocumentCategoryId() - 1];
        if (documentType == DocumentType.PURCHASE_ORDER) {             //TODO :handle below code for other required type
            handleConsumptionPR(report);
            return;
        }
        if (documentType == DocumentType.INVOICE) {
            handleConsumptionPO(report);
        }
    }

    private void handleConsumptionPR(DocumentApprovalContainer report) {
        logger.info("handleConsumptionPR: Starting consumption calculation for report {}", report.getId());

        PurchaseOrderHeader purchaseOrderHeader = report.getDocuments().get(0).getPoHeader();

        if (null == purchaseOrderHeader.getPurchaseOrderItems().get(0).getPurchaseRequestItemId() || null == purchaseOrderHeader.getPurchaseOrderItems().get(0).getPurchaseRequestItem())
            return;

        logger.info("handleConsumptionPR: Linking Purchase order items to PR's for report {}", report.getId());
        List<PurchaseOrderItem> purchaseOrderItems = purchaseOrderHeader.getPurchaseOrderItems();
//        // get associated PurchaseRequestItem and then map to its PurchaseRequest
        Map<PurchaseRequestHeader, List<PurchaseOrderItem>> map = purchaseOrderItems.stream()
                .collect(Collectors.groupingBy(i -> i.getPurchaseRequestItem().getPurchaseRequestHeader()));

        logger.info("handleConsumptionPR: Updating consumption for report {}", report.getId());
        //  Calculate and set the total consumed amount for each PurchaseRequest
        map.forEach((purchaseRequest, purchaseOrderItem) -> {
            BigDecimal totalConsumedAmount = purchaseOrderItem.stream()
                    .map(PurchaseOrderItem::getTotal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // get document object of the PR
            Document document = purchaseRequest.getDocument();
//            purchaseRequest.setConsumedAmount(getUpdatedConsumedAmount(report.getReportStatus(), purchaseRequest.getConsumedAmount(), totalConsumedAmount));
            document.setConsumedAmount(getUpdatedConsumedAmount(report.getReportStatus(), document.getConsumedAmount(), totalConsumedAmount)); // Todo Vishal: Check changes

            documentRepository.saveAndFlush(document);
            purchaseRequestHeaderRepository.saveAndFlush(purchaseRequest);
        });

        logger.info("handleConsumptionPR: returning");
    }


    private void handleConsumptionPO(DocumentApprovalContainer report) {
        logger.info("handleConsumptionPO: Starting consumption calculation for report {}", report.getId());
        InvoiceHeader invoiceHeader = report.getDocuments().get(0).getInvoiceHeader();

        if (null == invoiceHeader.getHasPrecedingDocument() || !invoiceHeader.getHasPrecedingDocument())
            return;

        logger.info("handleConsumptionPO: Linking Purchase order items to PR's for report {}", report.getId());
        List<InvoiceItem> invoiceItems = invoiceHeader.getInvoiceItems();
//        // In case of multi-PO based invoice, find and group items according to PurchaseOrderHeader - this will also include single PO invoice
        Map<PurchaseOrderHeader, List<InvoiceItem>> map = invoiceItems.stream()
                .collect(Collectors.groupingBy(i -> i.getPurchaseOrderItem().getPurchaseOrderHeader()));

        logger.info("handleConsumptionPO: Updating consumption for report {}", report.getId());
        // to handle if invoice amount is greater than PO amount
        BigDecimal sumOfRemainingAmount = BigDecimal.ZERO;
        BigDecimal sumOftotalConsumedAmount = BigDecimal.ZERO;
        //  Calculate and set the total consumed amount for each PurchaseOrder

        for (Map.Entry<PurchaseOrderHeader, List<InvoiceItem>> entry : map.entrySet()) {
            PurchaseOrderHeader purchaseOrderHeader = entry.getKey();
            List<InvoiceItem> invoiceItemsByPO = entry.getValue();

            BigDecimal totalConsumedAmount = invoiceItemsByPO.stream()
                    .map(InvoiceItem::getTotalWithTax)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            Document document = purchaseOrderHeader.getDocument();

            // Calculate sum of remaining amount for PO/s
            sumOfRemainingAmount = sumOfRemainingAmount.add(document.getClaimAmount()!=null?document.getClaimAmount():BigDecimal.ZERO.min(document.getConsumedAmount()!=null?document.getConsumedAmount():BigDecimal.ZERO));
            sumOftotalConsumedAmount = sumOftotalConsumedAmount.add(totalConsumedAmount);

            document.setConsumedAmount(
                    getUpdatedConsumedAmount(report.getReportStatus(), document.getConsumedAmount(), totalConsumedAmount)
            );

            documentRepository.save(document);
            purchaseOrderHeaderRepository.save(purchaseOrderHeader);
        }


        //  check if invoice amount is exceeding the total remaining amount of PO/s

        if (report.getReportStatus().equals(ReportStatus.SUBMITTED)) {
            if(sumOftotalConsumedAmount.compareTo(sumOfRemainingAmount) > 0){ // if greater, then returns 1
                throw new DomainInvariantException("Invoice amount is greater than PO amount");
            }
        }
        documentRepository.flush();
        purchaseOrderHeaderRepository.flush();
        logger.info("handleConsumptionPO: returning");
    }

    private BigDecimal getUpdatedConsumedAmount(ReportStatus status, BigDecimal originalConsumedAmount, BigDecimal totalConsumedAmount) {
        if (null == originalConsumedAmount)
            originalConsumedAmount = BigDecimal.ZERO;
        switch (status) {
            case SUBMITTED:
                return originalConsumedAmount.add(totalConsumedAmount);
            case SENT_BACK:
                return originalConsumedAmount.subtract(totalConsumedAmount);
            default:
                return originalConsumedAmount;
        }
    }
}
