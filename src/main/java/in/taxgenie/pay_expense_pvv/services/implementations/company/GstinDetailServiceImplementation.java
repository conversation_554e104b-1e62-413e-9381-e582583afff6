package in.taxgenie.pay_expense_pvv.services.implementations.company;

import com.fasterxml.jackson.databind.ObjectMapper;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.entities.ExternalServicesIdentifier;
import in.taxgenie.pay_expense_pvv.entities.company.GstinDetail;
import in.taxgenie.pay_expense_pvv.entities.company.PanDetail;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.invoice.repository.IStateCodeRepo;
import in.taxgenie.pay_expense_pvv.repositories.company.IGstinRepository;
import in.taxgenie.pay_expense_pvv.repositories.company.IPanDetailRepository;
import in.taxgenie.pay_expense_pvv.services.interfaces.company.IGstinDetailService;
import in.taxgenie.pay_expense_pvv.utils.MultiServiceClientFactory;
import in.taxgenie.pay_expense_pvv.utils.UrlConstants;
import in.taxgenie.pay_expense_pvv.viewmodels.company.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import reactor.core.scheduler.Schedulers;

@Service
@Transactional
public class GstinDetailServiceImplementation implements IGstinDetailService {
    private final IGstinRepository repository;
    private final IPanDetailRepository panDetailRepository;
    private final IStateCodeRepo stateCodeRepo;
    private final Logger logger;

    private final ObjectMapper objectMapper;
    private final com.taxgenie.utils.api.MultiServiceClient webClientHelper;

    private final in.taxgenie.pay_expense_pvv.MultiServiceClient webClientHelperV2;
    private final MultiServiceClientFactory multiServiceClientFactory;

    public GstinDetailServiceImplementation(IGstinRepository repository,
                                            IPanDetailRepository panDetailRepository, IStateCodeRepo stateCodeRepo,
                                            MultiServiceClientFactory multiServiceClientFactory,
                                            ObjectMapper objectMapper) {
        this.repository = repository;
        this.panDetailRepository = panDetailRepository;
        this.stateCodeRepo = stateCodeRepo;
        this.logger = LoggerFactory.getLogger(this.getClass());
        this.multiServiceClientFactory = multiServiceClientFactory;
        this.webClientHelper = multiServiceClientFactory.createMultiServiceClient(WebClient.builder());
        this.webClientHelperV2 = multiServiceClientFactory.createMultiServiceClientV2(WebClient.builder());
        this.objectMapper = objectMapper;
    }

    @Override
    public List<GstinDetailViewModel> getAllGstinDetails(IAuthContextViewModel auth) {
        authSanityCheck("getAllGstinDetails", auth);

        logger.info("getAllGstinDetails: Entered for company ID {}", auth.getCompanyCode());
        List<GstinDetailViewModel> viewModels;
        try {
            List<GstinDetail> gstinDetails = repository.findByCompanyCode(auth.getCompanyCode());
            if (gstinDetails == null) {
                gstinDetails = Collections.emptyList();
            }

            viewModels = gstinDetails.stream()
                    .map(this::getViewModel)
                    .collect(Collectors.toList());

            String resultInfo = viewModels.isEmpty()
                    ? String.format("Returning empty set. No GSTIN details found for company ID: %s", auth.getCompanyCode())
                    : String.format("Returning %d GSTIN details for company ID: %s", viewModels.size(), auth.getCompanyCode());

            logger.info("getAllGstinDetails: {}", resultInfo);
        } catch (Exception e) {
            logger.error("getAllGstinDetails: An error occurred while retrieving GSTIN details for company ID {}", auth.getCompanyCode(), e);
            throw new DomainInvariantException("Failed to retrieve GSTIN details"); // Use a custom exception
        }

        return viewModels;
    }

    public GstinDetailViewModel getViewModel(GstinDetail entity) {
        GstinDetailViewModel viewModel = new GstinDetailViewModel();

        viewModel.setId(entity.getId());
        viewModel.setCompanyCode(entity.getCompanyCode());
        viewModel.setGstinNo(entity.getGstinNo());

        if (entity.getRegistrationDate() != null) {
            viewModel.setRegistrationDate(entity.getRegistrationDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }

        if (entity.getGstinType() != null) {
            viewModel.setGstinType(entity.getGstinType().name());
        }

        viewModel.setFilingFrequency(entity.getFilingFrequency());
        viewModel.setLegalName(entity.getLegalName());
        viewModel.setLegalTradeName(entity.getLegalTradeName());
        viewModel.setPanNo(entity.getPanDetail().getPanNumber());
        viewModel.setStateCode(entity.getStateCode());
        String normalizedCode = String.format("%02d", Integer.parseInt(entity.getStateCode()));
        stateCodeRepo.findByCode(normalizedCode).ifPresent(stateCode -> {
            viewModel.setStateCodeId(stateCode.getId());
        });
        viewModel.setStringStateCode(entity.getStateCode());
        viewModel.setBuyerName(entity.getBuyerName());
        viewModel.setEmailId(entity.getEmailId());
        viewModel.setAddress(entity.getAddress());
        viewModel.setContactNumber(entity.getContactNumber());
        viewModel.setPinCode(entity.getPinCode());

        viewModel.setUserId(entity.getUserId());
        viewModel.setAuthorise(entity.isAuthorise());

        return viewModel;
    }

    @Override
    @Transactional
    public GstinDetailViewModel addGstinDetail(GstinDetailCreateViewModel viewModel, IAuthContextViewModel auth) {
        GstinDetail entity = null;
        authSanityCheck("addGstinDetail", auth);

        if (viewModel == null) {
            logger.error("addGstinDetail: viewModel is null");
            throw new IllegalArgumentException("GstinDetailCreateViewModel cannot be null");
        }

        logger.info("addGstinDetail: Entered for company ID {}", auth.getCompanyCode());

        try {
            logger.info("addGstinDetail: Checking whether details already exists for provided Gstin  for company ID {}", auth.getCompanyCode());
            Optional<GstinDetail> gstinDetailOptional = repository.findByCompanyCodeAndGstinNo(auth.getCompanyCode(), viewModel.getGstinNo());
            if (gstinDetailOptional.isPresent()) {
                throw new DomainInvariantException(String.format("GSTIN %s already exists for company ID %s", viewModel.getGstinNo(), auth.getCompanyCode()));

            }
            logger.info("addGstinDetail: Unique Gstin check passed, proceeding to create GST entry for company ID {}", auth.getCompanyCode());
            PanDetail panDetail = panDetailRepository.findByCompanyCode(auth.getCompanyCode())
                    .orElseThrow(() -> new RecordNotFoundException(
                            String.format("PAN details could not be found for company ID %s", auth.getCompanyCode())
                    ));
            entity = GstinDetail.intializeNewEntry(viewModel, panDetail, auth.getCompanyCode());

            logger.info("addGstinDetail: New entry created with GSTIN {} for company code {}. Performing the save.", entity.getGstinNo(), auth.getCompanyCode());
            repository.saveAndFlush(entity);
            logger.info("addGstinDetail: New entry saved with GSTIN {} for company code {}. Returning result.", entity.getGstinNo(), auth.getCompanyCode());
            return getViewModel(entity);

        } catch (DomainInvariantException | RecordNotFoundException e) {
            throw new DomainInvariantException(e.getMessage());
        } catch (DataIntegrityViolationException e) {
            logger.error("addGstinDetail: Duplicate GSTIN detected during save for GSTIN {} and company code {}", entity.getGstinNo(), auth.getCompanyCode(), e);
            throw new DomainInvariantException(String.format("GSTIN %s already exists for company code %s", entity.getGstinNo(), auth.getCompanyCode()));
        } catch (Exception e) {
            logger.error("addGstinDetail: There was an unhandled exception");
        }

        return null;
    }

    public List<GstinDetailViewModel> addGstinDetailsMultiGstinPerState(List<GstinDetailCreateViewModel> viewModels, IAuthContextViewModel auth) {
        authSanityCheck("addGstinDetailsMultiGstinPerState", auth);

        try {
            if (viewModels == null || viewModels.isEmpty()) {
                logger.error("addGstinDetailsMultiGstinPerState: viewModels is null or empty for company code {}", auth.getCompanyCode());
                throw new IllegalArgumentException("Gstin details not provided");
            }

            logger.info("addGstinDetailsMultiGstinPerState: Processing {} GSTIN detail(s) for company code {}", viewModels.size(), auth.getCompanyCode());
            validateUniqueGstins(viewModels, auth.getCompanyCode());
            logger.info("addGstinDetailsMultiGstinPerState: No duplicate GSTINs found for company code {}", auth.getCompanyCode());

            List<GstinDetailCreateViewModel> newEntries = viewModels.stream().filter(gstin -> gstin.getId() == null).toList();
            List<GstinDetailCreateViewModel> updatedEntries = viewModels.stream().filter(gstin -> gstin.getId() != null).toList();

            List<Long> idsToUpdate = viewModels.stream().map(GstinDetailCreateViewModel::getId).toList();

            // Sanity Check
            if (newEntries.size() + updatedEntries.size() != viewModels.size()) {
                logger.error("addGstinDetailsMultiGstinPerState: Mismatch in entry sizes. Check the system. New entries: {} Updated Entries: {} Total Entries: {}", newEntries.size(), updatedEntries.size(), viewModels.size());
                throw new DataIntegrityViolationException("The system is having issue updating the state details. Please try again later.");
            }

            HashMap<Long, GstinDetail> entriesToUpdateMap = new HashMap();
            repository.findByCompanyCodeAndIdIn(auth.getCompanyCode(), idsToUpdate)
                    .forEach(gstinDetail -> {
                        entriesToUpdateMap.put(gstinDetail.getId(), gstinDetail);
                    });

            List<GstinDetail> gstinsToCreate = new ArrayList<>();
            List<GstinDetail> gstinsToUpdate = new ArrayList<>();

            // Sanity check

            // Get pan detail
            PanDetail panDetail = panDetailRepository.findByCompanyCode(auth.getCompanyCode())
                    .orElseThrow(() -> new RecordNotFoundException(
                            String.format("PAN details could not be found for company ID %s", auth.getCompanyCode())
                    ));

            for (GstinDetailCreateViewModel createRequest : newEntries) {
                // Create new entry
                logger.debug("addGstinDetailsMultiGstinPerState: Creating new GSTIN detail for state code {}", GstinDetail.extractStateCode(createRequest.getGstinNo()));
                GstinDetail newDetail = GstinDetail.intializeNewEntry(createRequest, panDetail, auth.getCompanyCode());
                gstinsToCreate.add(newDetail);
            }

            for (GstinDetailCreateViewModel updateRequest : updatedEntries) {
                GstinDetail entryToUpdate = entriesToUpdateMap.get(updateRequest.getId());
                entryToUpdate.updateEntry(updateRequest);
                gstinsToUpdate.add(entryToUpdate);
            }

            // Persist changes
            logger.debug("addGstinDetailsMultiGstinPerState: Saving {} updated GSTIN detail(s)", gstinsToUpdate.size());
            List<GstinDetail> savedUpdatedGstins = repository.saveAll(gstinsToUpdate);

            logger.debug("addGstinDetailsMultiGstinPerState: Saving {} new GSTIN detail(s)", gstinsToCreate.size());
            List<GstinDetail> savedNewGstins = repository.saveAll(gstinsToCreate);

            List<GstinDetail> allSavedGstins = new ArrayList<>();
            allSavedGstins.addAll(savedUpdatedGstins);
            allSavedGstins.addAll(savedNewGstins);

            logger.info("addGstinDetailsMultiGstinPerState: Successfully processed GSTIN details for company code {}. Updated: {}, Created: {}", auth.getCompanyCode(), savedUpdatedGstins.size(), savedNewGstins.size());

            return allSavedGstins.stream()
                    .map(this::getViewModel)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("addGstinDetailsMultiGstinPerState: Error occurred while adding GSTIN details for company code {}: {}", auth.getCompanyCode(), e.getMessage());
            throw new DomainInvariantException("An error occurred while adding GSTIN details.");
        }

    }


    @Override
    @Transactional
    public List<GstinDetailViewModel> addGstinDetails(List<GstinDetailCreateViewModel> viewModels, IAuthContextViewModel auth) {
        authSanityCheck("addGstinDetails", auth);

        if (viewModels == null || viewModels.isEmpty()) {
            logger.error("addGstinDetails: viewModels is null or empty for company code {}", auth.getCompanyCode());
            throw new IllegalArgumentException("Gstin details not provided");
        }

        logger.info("addGstinDetails: Processing {} GSTIN detail(s) for company code {}", viewModels.size(), auth.getCompanyCode());
        duplicateGstinsCheck(viewModels, auth);
        logger.info("addGstinDetails: No duplicate GSTINs found for company code {}", auth.getCompanyCode());

        try {

            // Extract state codes
            logger.debug("addGstinDetails: Extracting state codes from GSTIN numbers");
            Map<String, GstinDetailCreateViewModel> inputGstinMap = viewModels.stream()
                    .collect(Collectors.toMap(
                            vm -> GstinDetail.extractStateCode(vm.getGstinNo()),
                            vm -> vm,
                            (existing, replacement) -> {
                                String stateCode = GstinDetail.extractStateCode(existing.getGstinNo());
                                logger.error("addGstinDetails: Duplicate GSTIN entry found for state code {}", stateCode);
                                throw new IllegalArgumentException("Duplicate GSTIN entries for state code: " + stateCode);
                            }
                    ));

            List<String> inputStateCodes = new ArrayList<>(inputGstinMap.keySet());
            logger.debug("addGstinDetails: Extracted state codes: {}", inputStateCodes);

            // Query existing entries
            logger.debug("addGstinDetails: Querying existing GSTIN details for company code {} and state codes {}", auth.getCompanyCode(), inputStateCodes);
            List<GstinDetail> existingGstinDetails = repository.findByCompanyCodeAndStateCodeIn(auth.getCompanyCode(), inputStateCodes);
            logger.debug("addGstinDetails: Found {} existing GSTIN detail(s) to update", existingGstinDetails.size());

            // Map existing entries by state code
            Map<String, GstinDetail> existingGstinMap = existingGstinDetails.stream()
                    .collect(Collectors.toMap(GstinDetail::getStateCode, gd -> gd));

            // Prepare lists for updates and creations
            List<GstinDetail> gstinsToUpdate = new ArrayList<>();
            List<GstinDetail> gstinsToCreate = new ArrayList<>();

            // Get pan detail
            PanDetail panDetail = panDetailRepository.findByCompanyCode(auth.getCompanyCode())
                    .orElseThrow(() -> new RecordNotFoundException(
                            String.format("PAN details could not be found for company ID %s", auth.getCompanyCode())
                    ));

            for (Map.Entry<String, GstinDetailCreateViewModel> entry : inputGstinMap.entrySet()) {
                String stateCode = entry.getKey();
                GstinDetailCreateViewModel vm = entry.getValue();

                if (existingGstinMap.containsKey(stateCode)) {
                    // Update existing entry
                    GstinDetail existingDetail = existingGstinMap.get(stateCode);
                    logger.debug("addGstinDetails: Updating existing GSTIN detail for state code {}", stateCode);
                    existingDetail.updateEntry(vm);
                    gstinsToUpdate.add(existingDetail);
                } else {
                    // Create new entry
                    logger.debug("addGstinDetails: Creating new GSTIN detail for state code {}", stateCode);
                    GstinDetail newDetail = GstinDetail.intializeNewEntry(vm, panDetail, auth.getCompanyCode());
                    gstinsToCreate.add(newDetail);
                }
            }

            // Persist changes
            logger.debug("addGstinDetails: Saving {} updated GSTIN detail(s)", gstinsToUpdate.size());
            List<GstinDetail> savedUpdatedGstins = repository.saveAll(gstinsToUpdate);

            logger.debug("addGstinDetails: Saving {} new GSTIN detail(s)", gstinsToCreate.size());
            List<GstinDetail> savedNewGstins = repository.saveAll(gstinsToCreate);

            List<GstinDetail> allSavedGstins = new ArrayList<>();
            allSavedGstins.addAll(savedUpdatedGstins);
            allSavedGstins.addAll(savedNewGstins);

            logger.info("addGstinDetails: Successfully processed GSTIN details for company code {}. Updated: {}, Created: {}", auth.getCompanyCode(), savedUpdatedGstins.size(), savedNewGstins.size());

            return allSavedGstins.stream()
                    .map(this::getViewModel)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("addGstinDetails: Error occurred while adding GSTIN details for company code {}: {}", auth.getCompanyCode(), e.getMessage());
            throw new DomainInvariantException("An error occurred while adding GSTIN details.");
        }
    }

// Helper methods...

    private void authSanityCheck(String functionName, IAuthContextViewModel auth) {
        if (auth == null) {
            logger.error("{}}: Auth context", functionName);
            throw new IllegalArgumentException("Auth context cannot be null");
        }
    }

    private void duplicateGstinsCheck(List<GstinDetailCreateViewModel> viewModels, IAuthContextViewModel auth) {
        validateUniqueGstins(viewModels, auth.getCompanyCode());

        // Note: Author Vedant .R. Dube - 13/03/2025
        // Currently commented this out as with the introduction with the ISD feature, a single state can have multiple gstins.
        // Not removed as the function is quite useful and if need it again just need to uncomment it here.
        validateDuplicateStateDetails(viewModels, auth.getCompanyCode());
    }

    /**
     * Validates that all GSTIN numbers in the provided list are unique for the specified company.
     *
     * @param viewModels  a list of {@code GstinDetailCreateViewModel} objects containing GSTIN details.
     * @param companyCode Provides the company code for logs
     * @throws IllegalArgumentException if duplicate GSTIN numbers are found.
     */
    public void validateUniqueGstins(List<GstinDetailCreateViewModel> viewModels, long companyCode) throws IllegalArgumentException {
        logger.info("validateUniqueGstins: Checking GSTINs are all unique for company code {}", companyCode);

        // Extract GSTIN numbers, filtering out any potential nulls
        List<String> gstinNumbers = viewModels.stream()
                .map(GstinDetailCreateViewModel::getGstinNo)
                .filter(Objects::nonNull)
                .toList();

        // Use a Set to filter out duplicates
        Set<String> uniqueGstinNumbers = new HashSet<>(gstinNumbers);

        if (gstinNumbers.size() != uniqueGstinNumbers.size()) {
            logger.error("Duplicate GSTINs found in the request for company code {}", companyCode);
            throw new IllegalArgumentException("There are duplicate GSTINs in the request.");
        }
    }

    /**
     * Validates that duplicate state details (determined by duplicate GSTIN prefixes) are not provided
     * in the payload for the specified company.
     * <p>
     * This method groups GSTIN numbers by their prefix (first two characters) and throws an
     * {@code IllegalArgumentException} if any prefix appears more than once. It also validates
     * that the input list is neither null nor empty, and that each GSTIN number is at least 2 characters long.
     *
     * @param viewModels  a list of {@code GstinDetailCreateViewModel} objects containing GSTIN details.
     * @param companyCode Provides the company code for logs
     * @throws IllegalArgumentException if the viewModels list is null or empty, if any GSTIN is invalid,
     *                                  or if duplicate GSTIN prefixes (i.e., duplicate state details) are found.
     */
    public void validateDuplicateStateDetails(List<GstinDetailCreateViewModel> viewModels, long companyCode) throws IllegalArgumentException {
        logger.info("addGstinDetails: Making sure that duplicate state details are not being provided in the payload for company code {}", companyCode);

        // Ensure viewModels is not null or empty
        if (viewModels == null || viewModels.isEmpty()) {
            throw new IllegalArgumentException("The viewModels list cannot be null or empty.");
        }

        // Group GSTINs by their prefixes (first two characters)
        Map<String, List<String>> gstinPrefixMap = viewModels.stream()
                .collect(Collectors.groupingBy(
                        vm -> {
                            String gstinNo = vm.getGstinNo();
                            if (gstinNo == null || gstinNo.length() < 2) {
                                throw new IllegalArgumentException("Each GSTIN number must be at least 2 characters long.");
                            }
                            return gstinNo.substring(0, 2);
                        },
                        Collectors.mapping(GstinDetailCreateViewModel::getGstinNo, Collectors.toList())
                ));

        // Find prefixes that have more than one GSTIN associated (duplicates)
        Map<String, List<String>> duplicatePrefixes = gstinPrefixMap.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (!duplicatePrefixes.isEmpty()) {
            // Create a detailed error message
            StringBuilder errorMessage = new StringBuilder();
            errorMessage.append(String.format("There are duplicate states provided in the payload for company code %s. ", companyCode));
            errorMessage.append("Duplicate GSTIN prefixes and their GSTINs: ");

            duplicatePrefixes.forEach((prefix, gstins) -> {
                errorMessage.append(String.format("Prefix '%s' has GSTINs %s; ", prefix, gstins));
            });

            throw new IllegalArgumentException(errorMessage.toString());
        }
    }


    // ? Send OTP Method
    @Override
    public ResponseViewModel sendOtpForGSTIN(SendOTPRequestViewModel requestViewModel, IAuthContextViewModel auth) {
        try {
            logger.info("Starting sendOtpForGSTIN for company: {} and GSTIN: {}", auth.getCompanyCode(), requestViewModel.getGstinNo());

            // Step 1: Database call first
            Optional<GstinDetail> optionalGstinDetail = repository.findByCompanyCodeAndGstinNo(auth.getCompanyCode(), requestViewModel.getGstinNo());

            if (optionalGstinDetail.isEmpty()) {
                logger.warn("GSTIN not found for company: {}", auth.getCompanyCode());
                return new ResponseViewModel(false, "GSTIN not found");
            }

            GstinDetail gstinDetail = optionalGstinDetail.get();

            // Step 2: Set Developer Attributes
            try {
                setDeveloperAttributes(requestViewModel.getGstinNo(), requestViewModel.getUserId(), auth)
                        .block();
            } catch (Exception e) {
                logger.error("Failed to set developer attributes: {}", e.getMessage(), e);
                return new ResponseViewModel(false, "Something went wrong, please contact Administrator");
            }

            // Step 3: Get Access Token
            OAuthProxyViewModel oAuthProxyViewModel;
            try {
                oAuthProxyViewModel = getAccessTokenWithKeys(auth).block();
                if (oAuthProxyViewModel == null) {
                    logger.error("Failed to obtain access token");
                    return new ResponseViewModel(false, "Something went wrong, please contact Administrator");
                }
            } catch (Exception e) {
                logger.error("Failed to get access token: {}", e.getMessage(), e);
                return new ResponseViewModel(false, "Something went wrong, please contact Administrator");
            }

            // Step 4: Generate OTP
            GenerateOtpResponseViewModel otpResponse;
            try {
                otpResponse = generateOtp(
                        oAuthProxyViewModel.getTokenType() + " " + oAuthProxyViewModel.getAccessToken(),
                        requestViewModel.getGstinNo(), auth).block();

                if (otpResponse == null) {
                    logger.error("Failed to generate OTP");
                    return new ResponseViewModel(false, "Failed to generate OTP");
                }

            } catch (Exception e) {
                logger.error("Failed to generate OTP: {}", e.getMessage(), e);
                return new ResponseViewModel(false, "Failed to generate OTP");
            }

            boolean otpSuccess = "1".equals(otpResponse.getStatusCd());

            // Step 5: Update GSTIN Detail
            try {
                updateGstinDetailWithUserId(gstinDetail, requestViewModel.getUserId()).block();
            } catch (Exception e) {
                logger.error("Failed to update GSTIN detail: {}", e.getMessage(), e);
                return new ResponseViewModel(false, "Failed to update GSTIN detail");
            }

            return new ResponseViewModel(
                    otpSuccess,
                    otpSuccess ? "OTP sent successfully" : "Failed to send OTP"
            );

        } catch (Exception e) {
            logger.error("sendOtpForGSTIN: Exception occurred: {}", e.getMessage(), e);
            return new ResponseViewModel(false, "Something went wrong, please try again later...");
        }
    }


    // ? Verify OTP Method
    @Override
    public ResponseViewModel verifyOtpForGSTIN(VerifyOTPRequestViewModel requestViewModel, IAuthContextViewModel auth) {
        try {
            logger.info("Starting verifyOtpForGSTIN for company: {} and GSTIN: {}", auth.getCompanyCode(), requestViewModel.getGstinNo());

            // Step 1: Database call first
            Optional<GstinDetail> optionalGstinDetail = repository.findByCompanyCodeAndGstinNo(auth.getCompanyCode(), requestViewModel.getGstinNo());

            if (optionalGstinDetail.isEmpty()) {
                logger.warn("GSTIN not found for company: {}", auth.getCompanyCode());
                return new ResponseViewModel(false, "GSTIN not found");
            }

            GstinDetail gstinDetail = optionalGstinDetail.get();

            // Step 2: Get Access Token
            OAuthProxyViewModel oAuthProxyViewModel;
            try {
                oAuthProxyViewModel = getAccessTokenWithKeys(auth).block();
                if (oAuthProxyViewModel == null) {
                    logger.error("Failed to obtain access token");
                    return new ResponseViewModel(false, "Something went wrong, please contact Administrator");
                }
            } catch (Exception e) {
                logger.error("Failed to get access token: {}", e.getMessage(), e);
                return new ResponseViewModel(false, "Something went wrong, please contact Administrator");
            }

            // Step 3: Verify OTP
            VerifyOtpResponseViewModel verifyOtpResponse;
            try {
                verifyOtpResponse = verifyOtp(
                        oAuthProxyViewModel.getTokenType() + " " + oAuthProxyViewModel.getAccessToken(),
                        requestViewModel, auth).block();

                if (verifyOtpResponse == null) {
                    logger.error("Failed to verify OTP");
                    return new ResponseViewModel(false, "Failed to verify OTP");
                }

            } catch (Exception e) {
                logger.error("Failed to verify OTP: {}", e.getMessage(), e);
                return new ResponseViewModel(false, "Failed to verify OTP");
            }

            boolean otpSuccess = "success".equalsIgnoreCase(verifyOtpResponse.getStatus());

            // Step 4: Update Authorise Status
            try {
                updateAuthoriseStatus(gstinDetail, otpSuccess).block();
            } catch (Exception e) {
                logger.error("Failed to update authorise status: {}", e.getMessage(), e);
                return new ResponseViewModel(false, "Failed to update authorise status");
            }

            return new ResponseViewModel(
                    otpSuccess,
                    otpSuccess ? "OTP verified successfully" : verifyOtpResponse.getError()
            );

        } catch (Exception e) {
            logger.error("verifyOtpForGSTIN: Exception occurred: {}", e.getMessage(), e);
            return new ResponseViewModel(false, "Something went wrong, please try again later...");
        }
    }


    private Mono<Void> updateAuthoriseStatus(GstinDetail gstinDetail, boolean isAuthorised) {
        return Mono.fromRunnable(() -> {
            gstinDetail.setAuthorise(isAuthorised); // ? Update the "authorise" field
            repository.save(gstinDetail); // ? Save the updated entity
            logger.info("Authorise status updated to {} for GSTIN: {}", isAuthorised, gstinDetail.getGstinNo());
        }).subscribeOn(Schedulers.boundedElastic()).then(); // Run DB update on a separate thread
    }

    private Mono<Void> updateGstinDetailWithUserId(GstinDetail gstinDetail, String userId) {
        return Mono.fromRunnable(() -> {
            gstinDetail.setUserId(userId); // ? Update userId field
            repository.save(gstinDetail);  // ? Save the updated entity
            logger.info("User ID {} updated in GSTIN detail for GSTIN: {}", userId, gstinDetail.getGstinNo());
        }).subscribeOn(Schedulers.boundedElastic()).then(); // Run DB update asynchronously
    }

    private Mono<Void> setDeveloperAttributes(String gstin, String userName, IAuthContextViewModel auth) {
        Map<String, Object> requestJson = createJsonForDeveloperAttributes(gstin, userName);

        return webClientHelper.makeRequest(
                        ExternalServicesIdentifier.APIGEE_ACCOUNT_SERVICE.ordinal(),
                        HttpMethod.POST,
                        UrlConstants.DEVELOPER_ATTRIBUTES,
                        Optional.of(requestJson),
                        null,
                        null,
                        String.class,
                        auth.getToken()
                )
                .doOnSuccess(response -> logger.info("Developer attributes set successfully"))
                .doOnError(error -> logger.error("Failed to set developer attributes: {}", error.getMessage()))
                .then(); // Ensures Mono<Void> is returned
    }

    private Mono<GenerateOtpResponseViewModel> generateOtp(String token, String gstinNo, IAuthContextViewModel auth) {
        Map<String, String> headers = Map.of(
                "Authorization", token,
                "gstin", gstinNo
        );

        return webClientHelperV2.makeRequest(
                        "application/json",
                        ExternalServicesIdentifier.API4BUSINESS_SERVICE.ordinal(),
                        HttpMethod.POST,
                        UrlConstants.API4_BUSINESS_GENERATE_OTP,
                        Optional.of("{}"),
                        null,
                        null,
                        GenerateOtpResponseViewModel.class,
                        null,
                        headers
                )
                .doOnSuccess(response -> logger.info("Successfully generated OTP"))
                .doOnError(error -> logger.error("Failed to generate OTP: {}", error.getMessage()));
    }

    private Mono<VerifyOtpResponseViewModel> verifyOtp(String token, VerifyOTPRequestViewModel requestViewModel, IAuthContextViewModel auth) {
        Map<String, String> headers = Map.of(
                "Authorization", token,
                "gstin", requestViewModel.getGstinNo()
        );

        return webClientHelperV2.makeRequest(
                        "application/json",
                        ExternalServicesIdentifier.API4BUSINESS_SERVICE.ordinal(),
                        HttpMethod.POST,
                        UrlConstants.API4_BUSINESS_VERIFY_OTP,
                        Optional.of(requestViewModel),
                        null,
                        null,
                        VerifyOtpResponseViewModel.class,
                        null,
                        headers
                )
                .doOnSuccess(response -> logger.info("Successfully verified OTP for GSTIN {}", requestViewModel.getGstinNo()))
                .doOnError(error -> logger.error("Failed to verify OTP for GSTIN {}: {}", requestViewModel.getGstinNo(), error.getMessage()));
    }

    @Override
    public Mono<OAuthProxyViewModel> getAccessTokenWithKeys(IAuthContextViewModel auth) {
        return webClientHelper.makeRequest(
                        ExternalServicesIdentifier.APIGEE_ACCOUNT_SERVICE.ordinal(),
                        HttpMethod.GET,
                        UrlConstants.DEVELOPER_APP_KEYS,
                        Optional.empty(),
                        Map.of("environment", "sandbox"),
                        null,
                        DeveloperAppKeysResponseViewModel.class,
                        auth.getToken()
                )
                .doOnSuccess(devKeys -> logger.info("Successfully retrieved developer app keys"))
                .doOnError(error -> logger.error("Failed to retrieve developer app keys: {}", error.getMessage()))
                .flatMap(devKeys -> {
                    String consumerKey = devKeys.getConsumerKey();
                    String consumerSecret = devKeys.getConsumerSecret();

                    String authHeader = "Basic " + Base64.getEncoder()
                            .encodeToString((consumerKey + ":" + consumerSecret).getBytes());

                    Map<String, String> headers = Map.of(
                            "Authorization", authHeader,
                            "Content-Type", "application/x-www-form-urlencoded"
                    );

                    MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
                    formData.add("grant_type", "client_credentials");

                    return webClientHelperV2.makeRequest(
                            "application/x-www-form-urlencoded",
                            ExternalServicesIdentifier.API4BUSINESS_SERVICE.ordinal(),
                            HttpMethod.POST,
                            UrlConstants.API4_BUSINESS_OAUTH_PROXY,
                            Optional.of(formData),
                            null,
                            null,
                            OAuthProxyViewModel.class,
                            null,
                            headers
                    );
                })
                .doOnSuccess(token -> logger.info("Successfully retrieved access token"))
                .doOnError(error -> logger.error("Failed to retrieve access token: {}", error.getMessage()));
    }

    public Map<String, Object> createJsonForDeveloperAttributes(String gstNumber, String govUsername) {
        Map<String, Object> gstAttribute = new HashMap<>();
        Map<String, String> gstDetails = new HashMap<>();
        gstDetails.put("govUsername", govUsername);
        gstAttribute.put(gstNumber, gstDetails);

        Map<String, Object> finalJson = new HashMap<>();
        finalJson.put("gstAttribute", gstAttribute);

        return finalJson;
    }

}
