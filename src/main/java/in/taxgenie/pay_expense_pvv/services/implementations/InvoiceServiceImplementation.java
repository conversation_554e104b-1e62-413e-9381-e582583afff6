package in.taxgenie.pay_expense_pvv.services.implementations;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import com.taxgenie.utils.api.MultiServiceClient;
import in.taxgenie.pay_expense_pvv.auth.IAuthContextViewModel;
import in.taxgenie.pay_expense_pvv.cloud.GcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.cloud.interfaces.IGcpCSFileIOProvider;
import in.taxgenie.pay_expense_pvv.entities.*;
import in.taxgenie.pay_expense_pvv.entities.budget.Budget;
import in.taxgenie.pay_expense_pvv.entities.company.GstinDetail;
import in.taxgenie.pay_expense_pvv.entities.erp.POGrnMapping;
import in.taxgenie.pay_expense_pvv.entities.gl_master.GlMaster;
import in.taxgenie.pay_expense_pvv.entities.invoice.InvoiceItem;
import in.taxgenie.pay_expense_pvv.entities.invoice.Mailroom;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderHeader;
import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import in.taxgenie.pay_expense_pvv.entities.pr.ItemType;
import in.taxgenie.pay_expense_pvv.exceptions.DomainInvariantException;
import in.taxgenie.pay_expense_pvv.exceptions.DuplicateRecordFoundException;
import in.taxgenie.pay_expense_pvv.exceptions.RecordNotFoundException;
import in.taxgenie.pay_expense_pvv.govrndto.*;
import in.taxgenie.pay_expense_pvv.invoice.mapper.InvoiceHeaderMapper;
import in.taxgenie.pay_expense_pvv.invoice.mapper.LineItemsMapper;
import in.taxgenie.pay_expense_pvv.invoice.message.AmountDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.CompanyDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.InvoiceDocumentDetails;
import in.taxgenie.pay_expense_pvv.invoice.repository.ICountriesRepository;
import in.taxgenie.pay_expense_pvv.invoice.repository.IInvoiceHeaderRepository;
import in.taxgenie.pay_expense_pvv.invoice.repository.IStateCodeRepo;
import in.taxgenie.pay_expense_pvv.invoice.request.InvoiceDetailsReq;
import in.taxgenie.pay_expense_pvv.repositories.*;
import in.taxgenie.pay_expense_pvv.repositories.budget.IBudgetRepository;
import in.taxgenie.pay_expense_pvv.repositories.company.IGstinRepository;
import in.taxgenie.pay_expense_pvv.repositories.implementations.CustomDocumentApprovalContainerRepository;
import in.taxgenie.pay_expense_pvv.repositories.invoice.IInvoiceItemRepository;
import in.taxgenie.pay_expense_pvv.repositories.mailroom.IMailRoomReposiitory;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderHeaderRepository;
import in.taxgenie.pay_expense_pvv.repositories.po.IPurchaseOrderItemRepository;
import in.taxgenie.pay_expense_pvv.services.implementations.budget.BudgetServiceImplementation;
import in.taxgenie.pay_expense_pvv.services.interfaces.*;
import in.taxgenie.pay_expense_pvv.services.interfaces.documents.IDocumentManagementService;
import in.taxgenie.pay_expense_pvv.services.interfaces.dynamic.form.IDynamicFormService;
import in.taxgenie.pay_expense_pvv.services.interfaces.masters.IRatioCategoryMasterService;
import in.taxgenie.pay_expense_pvv.utils.*;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentApprovalContainerViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.DocumentCreateResponse;
import in.taxgenie.pay_expense_pvv.viewmodels.InvoiceApprovalContainerFromIRNViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.aggerates.ConsumedItemAvailableCountViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetSelectionViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.erp.ERPGrnViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice.*;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.BuyerDocIdRequestViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.invoice_ocr.StatusUpdateViewModel;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SegmentRatioToEntityRequest;
import org.eclipse.jdt.core.compiler.InvalidInputException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class InvoiceServiceImplementation implements IInvoiceService {

    public static final String INVOICE = "invoices/";
    public static final String SUPPORTING_DOCUMENTS = "additional_suuporting_documents_to_invoices/";
    private final IDocumentRepository documentRepository;
    private final IDocumentApprovalContainerRepository reportRepository;
    private final IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository;
    private final IDocumentRuleRepository ruleRepository;
    private final IDocumentSubgroupRepository subgroupRepository;
    private final IEmployeeMasterDataService employeeService;
    private final ILocationRepository locationRepository;
    private final IMetadataLimitRuleRepository metadataLimitRuleRepository;
    private final IFileIOService fileIOService;
    private final IInvoiceHeaderRepository invoiceHeaderRepository;
    private final IInvoiceItemRepository invoiceItemRepository;
    private final IItemMasterRepository itemMasterRepository;
    private final IPurchaseOrderItemRepository purchaseOrderItemRepository;
    private final IInvoiceReceivedRepository invoiceReceivedRepository;
    private final ILookupRepository lookupRepository;
    private final IDocumentApprovalContainerUserService documentApprovalContainerUserService;
    private final ICompanyRepository companyRepository;
    private final IGstinRepository gstinRepository;
    private final IStateCodeRepo stateCodeRepo;
    private final ILineItemDetailsRepository lineItemDetailsRepository;
    private final IBudgetRepository budgetRepository;
    private final IGlMasterRepository glMasterRepository;
    private final IDMRService idmrService;
    private final CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository;
    private final BudgetServiceImplementation budgetServiceImplementation;
    private final IDocumentManagementService documentManagementService;
    private final IDynamicFormService dynamicFormService;
    private final IRatioCategoryMasterService ratioCategoryMasterService;
    private final IInvoiceMailroomService invoiceMailroomService;
    private final IMailRoomReposiitory mailRoomReposiitory;
    private final ICountriesRepository countriesRepository;

    private final GcpCSFileIOProvider gsFileIOProvider;
    private final MultiServiceClient webClientHelper;
    private final MultiServiceClientFactory multiServiceClientFactory;
    private final ObjectMapper objectMapper;
    private final Logger logger;

    private final IPubSubPublisherService pubSubPublisherService;
    private final IEnvironmentDataProvider environmentDataProvider;

    private final IGcpCSFileIOProvider gcpCSFileIOProvider;

    @Value("${cloud.uploadEnv}")
    private String uploadEnv;


    public InvoiceServiceImplementation(IDocumentRepository documentRepository,
                                        IDocumentApprovalContainerRepository reportRepository, IPurchaseOrderHeaderRepository purchaseOrderHeaderRepository,
                                        IDocumentRuleRepository ruleRepository,
                                        IDocumentSubgroupRepository subgroupRepository,
                                        IEmployeeMasterDataService employeeService,
                                        ILocationRepository locationRepository,
                                        IMetadataLimitRuleRepository metadataLimitRuleRepository,
                                        IFileIOService fileIOService,
                                        InvoiceHeaderMapper invoiceHeaderMapper,
                                        IInvoiceHeaderRepository invoiceHeaderRepository,
                                        IInvoiceReceivedRepository invoiceReceivedRepository,
                                        ILookupRepository lookupRepository,
                                        IDocumentApprovalContainerUserService documentApprovalContainerUserService,
                                        ICompanyRepository companyRepository, IGstinRepository gstinRepository, IStateCodeRepo stateCodeRepo,
                                        ILineItemDetailsRepository lineItemDetailsRepository,
                                        BudgetServiceImplementation budgetServiceImplementation,
                                        GcpCSFileIOProvider gsFileIOProvider,
                                        ObjectMapper objectMapper,
                                        MultiServiceClientFactory multiServiceClientFactory,
                                        IDocumentApprovalContainerRepository documentApprovalContainerRepository,
                                        IInvoiceItemRepository invoiceItemRepository,
                                        IItemMasterRepository itemMasterRepository,
                                        IPurchaseOrderItemRepository purchaseOrderItemRepository,
                                        IBudgetRepository budgetRepository,
                                        IGlMasterRepository glMasterRepository,
                                        IDMRService idmrService,
                                        CustomDocumentApprovalContainerRepository customDocumentApprovalContainerRepository,
                                        IDocumentManagementService documentManagementService, IDynamicFormService dynamicFormService, IRatioCategoryMasterService ratioCategoryMasterService, IInvoiceMailroomService invoiceMailroomService, IMailRoomReposiitory mailRoomReposiitory, ICountriesRepository countriesRepository, IPubSubPublisherService pubSubPublisherService, IEnvironmentDataProvider environmentDataProvider, IGcpCSFileIOProvider gcpCSFileIOProvider,
                                        @Value("${cloud.uploadEnv}") String uploadEnv) {
        this.documentRepository = documentRepository;
        this.reportRepository = reportRepository;
        this.purchaseOrderHeaderRepository = purchaseOrderHeaderRepository;
        this.ruleRepository = ruleRepository;
        this.subgroupRepository = subgroupRepository;
        this.employeeService = employeeService;
        this.locationRepository = locationRepository;
        this.metadataLimitRuleRepository = metadataLimitRuleRepository;
        this.fileIOService = fileIOService;
        this.invoiceHeaderRepository = invoiceHeaderRepository;
        this.invoiceReceivedRepository = invoiceReceivedRepository;
        this.lookupRepository = lookupRepository;
        this.documentApprovalContainerUserService = documentApprovalContainerUserService;
        this.companyRepository = companyRepository;
        this.gstinRepository = gstinRepository;
        this.stateCodeRepo = stateCodeRepo;
        this.lineItemDetailsRepository = lineItemDetailsRepository;
        this.budgetServiceImplementation = budgetServiceImplementation;
        this.gsFileIOProvider = gsFileIOProvider;
        this.objectMapper = objectMapper;
        this.multiServiceClientFactory = multiServiceClientFactory;
        this.webClientHelper = multiServiceClientFactory.createMultiServiceClient(WebClient.builder());
        this.invoiceItemRepository = invoiceItemRepository;
        this.itemMasterRepository = itemMasterRepository;
        this.purchaseOrderItemRepository = purchaseOrderItemRepository;
        this.budgetRepository = budgetRepository;
        this.glMasterRepository = glMasterRepository;
        this.idmrService = idmrService;
        this.customDocumentApprovalContainerRepository = customDocumentApprovalContainerRepository;
        this.documentManagementService = documentManagementService;
        this.dynamicFormService = dynamicFormService;
        this.ratioCategoryMasterService = ratioCategoryMasterService;
        this.invoiceMailroomService = invoiceMailroomService;
        this.mailRoomReposiitory = mailRoomReposiitory;
        this.countriesRepository = countriesRepository;
        this.pubSubPublisherService = pubSubPublisherService;
        this.environmentDataProvider = environmentDataProvider;
        this.gcpCSFileIOProvider = gcpCSFileIOProvider;
        this.uploadEnv = uploadEnv;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    @Transactional
    public DocumentCreateResponse create(long metadataId, long subgroupId, String invoiceRequest, List<MultipartFile> invoices, IAuthContextViewModel auth) {
        DocumentCreateResponse response = new DocumentCreateResponse();

        String irn = "";
        try {
            // check if json is already available against pdf
            InvoiceDetailsReq invoiceDetailsRequest = objectMapper.readValue(invoiceRequest, InvoiceDetailsReq.class);
            InvoiceDTO govtInvJsonDto = new InvoiceDTO();
            final Map<String, Object> mapToHoldGovtJsonData = new HashMap<>();

            // Upload handling
            //   PDF
            InvoiceReceived invoiceReceived = null;
            String filename = null;
            URL signedUrl = null;

            DocumentApprovalContainer dac = null;
            //  Json
            File file = null;

            // Document Approval Container and document storing
            Document document = null;
            DocumentApprovalContainerViewModel viewModel = null;
            DocumentApprovalContainer documentApprovalContainer = null;
            DocumentSubgroup subgroup = null;
            InvoiceHeader invoiceHeader = null;

            // Lookup handling
            String uploadMethod = !invoiceDetailsRequest.getIsJson() ? StaticDataRegistry.LOOKUP_VALUE_PDF : StaticDataRegistry.LOOKUP_VALUE_JSON;
            LookupData documentType = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_INVOICE)
                    .orElseThrow(() -> {
                        logger.error(String.format("create: Lookup table error. Could not find document of type: %s and value: %s", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_INVOICE));
                        return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_NOT_FOUND);
                    });

            if (!invoiceDetailsRequest.getIsJson()) { // for pdf upload

                // check valid file type
                FileUtils.validateFileType(invoices.get(0), StringConstants.PDF_FILE);

                // get IRN
                irn = invoices.isEmpty() ? null
                        : getIRN(invoices.get(0), auth);

                invoiceReceived = getInvoiceByIRN(irn, auth);

                // check if invoice - pdf/json with same IRN is already available
                if (null != invoiceReceived) {
                    dac = invoiceReceived.getInvoiceHeader().getDocument().getDocumentApprovalContainer();
                    // this check is for P2P- 1635 - erp sync - re-upload -
                    // TODO : later handle invoice json related logic separately.
                    if (dac.getReportStatus() != ReportStatus.CANCELLED && dac.getReportStatus() != ReportStatus.DELETED && dac.getReportStatus() != ReportStatus.FAILED) {
                        // check if json is available or both json and pdf are available and matched
                        if (null == invoiceReceived.getGovtJson() || null != invoiceReceived.getJsonPdfMatchedStatus()) {
                            logger.info("Duplicate IRN - Invoice PDF already available for invoice header ");
                            throw new DomainInvariantException("Duplicate IRN - Invoice PDF already available");
                        }
                        // upload invoice and set url against uploaded json
                        filename = fileIOService.handleInvoiceUpload(invoices.get(0), auth.getCompanyCode());
                        signedUrl = fileIOService.getSignedUrl(filename);
                        invoiceReceived.setDocumentUrl(filename);
                        invoiceReceived.setJsonPdfMatchedStatus(lookupRepository.findByLookupDataCode(UUID.fromString(StaticDataRegistry.LOOKUP_CODE_JSON_MATCH)));
                        invoiceReceived.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());


                        dac.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());

                        invoiceReceivedRepository.saveAndFlush(invoiceReceived);
                        reportRepository.saveAndFlush(dac);

                        response.setMessage("Match found, PDF is linked with existing Json with IRN:" + irn);
                        response.setContainerId(dac.getId());
                        return response;
                    }
                }
            } else {
                // for json upload
                if (invoices != null && !invoices.isEmpty()) {
                    file = convert(invoices.get(0));
                    FileUtils.validateFileType(invoices.get(0), StringConstants.JSON_FILE);
                    // TODO : get IRN (source?), check if json already available against IRN
                    govtInvJsonDto = parseJsonFile(file);
                    MappersUtil.flattenHelper(govtInvJsonDto, "", mapToHoldGovtJsonData);

                    // check if IRN available - duplicate json upload
                    if (null != getInvoiceByIRN(Optional.ofNullable(govtInvJsonDto).map(InvoiceDTO::getIrn).orElse(null), auth)) {
                        logger.info("Duplicate IRN - JSON already available for IRN ");
                        throw new DomainInvariantException("Duplicate IRN - Json already available for IRN");
                    }
                } else {
                    throw new NullPointerException("No Invoice");
                }
                /** TODO:
                 * In case of Json upload, check IRN available (if extracted from Qrcode service - PDF upload)
                 **/
            }

            viewModel = documentApprovalContainerUserService.create(metadataId, auth);

            documentApprovalContainer = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), viewModel.getId())
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find documentApprovalContainer with id: %d", metadataId)));

            if (!isParentExpenseReportSubmittable(documentApprovalContainer)) {
                throw new DomainInvariantException("Parent expense documentApprovalContainer is neither in draft or sent-back state; hence not submittable");
            }

            subgroup = subgroupRepository.findByCompanyCodeAndId(auth.getCompanyCode(), subgroupId)
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find subgroup with id: %d", subgroupId)));

            if (subgroup.isFrozen()) {
                throw new DomainInvariantException("Subgroup is frozen");
            }

            logger.info("create: Creating a new Invoice Document entity");
            document = new Document();
            createDocument(document, documentApprovalContainer, subgroup, uploadMethod, auth);

            invoiceHeader = new InvoiceHeader();
            invoiceReceived = null;


            if (invoiceDetailsRequest.getIsJson()) {

                logger.info("preparing document information from json");
                prepareDocumentFromJson(document, govtInvJsonDto);

                logger.info("preparing invoice header and line items from json");
                InvoiceHeaderMapper.prepareInvoiceHeaderFromJson(invoiceHeader, govtInvJsonDto, document);

                handleLookupData(invoiceHeader, govtInvJsonDto);

                logger.info("preparing seller details from json");
                prepareSellerDetails(invoiceHeader, govtInvJsonDto, auth.getCompanyCode());
                logger.info("preparing invoice received from json");
                invoiceReceived = InvoiceHeaderMapper.prepareInvoiceReceivedFromJson(govtInvJsonDto, auth.getCompanyCode(), invoiceHeader); // TODO: discuss - get details from UI
                invoiceReceived.setGovtJson(objectMapper.writeValueAsString(govtInvJsonDto));

            } else {
                if (invoices.isEmpty()) {
                    throw new DomainInvariantException("An invoice document is required.");
                }
                if (invoices.size() > 1) {
                    throw new DomainInvariantException("Only a single invoice document is allowed.");
                }
                FileUtils.validateFileType(invoices.get(0), StringConstants.PDF_FILE);
                MultipartFile singleInvoice = invoices.get(0);
                filename = null;
                logger.info("Performing upload");
                filename = fileIOService.handleInvoiceUpload(singleInvoice, auth.getCompanyCode());
                signedUrl = gcpCSFileIOProvider.getSignedUrl(filename);

                initialiseDocument(document, invoiceDetailsRequest);
                InvoiceHeaderMapper.prepareInvoiceHeader(invoiceHeader, invoiceDetailsRequest, document);
                invoiceReceived = InvoiceHeaderMapper.prepareInvoiceReceived(invoiceDetailsRequest, auth.getCompanyCode(), invoiceHeader, filename, irn);

            }
            documentApprovalContainer.getDocuments().add(document);
            subgroup.getDocuments().add(document);

            logger.info("create: Saving both Document and Document Approval Container");
            documentRepository.saveAndFlush(document);
            reportRepository.saveAndFlush(documentApprovalContainer);
            subgroupRepository.saveAndFlush(subgroup);
            logger.info("create: Save successful");

            invoiceHeader.setHasPrecedingDocument(subgroup.getHasPrecedingDocument());
            invoiceHeader.setDocType(documentType);
            invoiceHeader.setDocTypeId(1); // invoice
            invoiceHeader.setCreatingUserId(auth.getUserId());

            documentApprovalContainer.setReportStatus(ReportStatus.PROCESSING);

            invoiceHeaderRepository.save(invoiceHeader);
            invoiceReceivedRepository.save(invoiceReceived);
            invoiceHeader.setInvoiceReceived(invoiceReceived);

            invoiceReceivedRepository.flush();
            invoiceHeaderRepository.flush();
            response.setContainerId(documentApprovalContainer.getId());

            //Call for ocr invoice upload here
            logger.info("calling uploadMultipartFileToIPU method of idmrService");
            if (null != signedUrl) {
                MultipartFile multipartFile = invoices.get(0);
                String invoiceStatus = idmrService.uploadMultipartFileToIPU(signedUrl.toString(), invoiceReceived.getInvoiceReceivedId(), documentApprovalContainer.getDocumentIdentifier(), auth);
            }

        } catch (Exception e) {
            logger.error("Exception occurred process Invoice " + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        return response;
    }

    void handleLookupData(InvoiceHeader invoiceHeader, InvoiceDTO govtInvJsonDto) {

        logger.info("In side handleLookupData method");

        if (govtInvJsonDto.getExpDtls() != null) {

            ExpDtlsDTO expDtls = govtInvJsonDto.getExpDtls();

            if (expDtls.getForCur() != null) {
                Optional<LookupData> lookupDataOptional = lookupRepository.findFirstByValue(expDtls.getForCur());
                lookupDataOptional.ifPresent(invoiceHeader::setForCur);
            }

            if (expDtls.getCntCode() != null) {
                Optional<Countries> countryOptional = countriesRepository.findByCode(expDtls.getCntCode());
                countryOptional.ifPresent(invoiceHeader::setCntCode);
            }
        }
    }

    private void addUpdateLineItemDetailsOld(List<InvoiceItemDetailsViewModel> itemDetailsViewModel, InvoiceHeader invoiceHeader, IAuthContextViewModel auth, Document document) {
        if (null == itemDetailsViewModel) {
            return;
        }
        // get isPoBased from invoiceHeader.getDocument().getDocumentSubgroup().isPoBased
        if (null == invoiceHeader.getDocTypeId()) {
            logger.error(String.format("Create Invoice line items, No Invoice doc type present for invoice id : %d", invoiceHeader.getInvoiceHeaderId()));
            throw new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
        }

        if (invoiceHeader.getHasPrecedingDocument()) { // PO based

            // override - delete old items belong to PO
            deleteAllLineItems(invoiceHeader.getInvoiceHeaderId());
            invoiceHeader.getInvoiceItems().clear();
            // intialize total PO value
//            invoiceHeader.setTotalAmount(new BigDecimal(0.0));
            document.setInitialAmounts();
//            document.setClaimAmount(new BigDecimal(0.0)); // Todo Vishal: Check changes
            invoiceHeaderRepository.saveAndFlush(invoiceHeader);

            List<InvoiceItem> lineItems = new ArrayList<>();
            itemDetailsViewModel.forEach(viewModel -> {

                lineItems.add(addItemFromPurchaseOrderItem(viewModel, invoiceHeader, auth, document));
            });

            // validate if Invoice amount is greater than PO amount - 2045 - commented/comment done
//            BigDecimal total = lineItems.stream()
//                    .map(InvoiceItem::getTotalWithTax)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);


            invoiceHeader.getInvoiceItems().addAll(lineItems);
            invoiceItemRepository.saveAllAndFlush(lineItems);
            documentRepository.saveAndFlush(document);

        } else {
            // Non PO based
            // reset invoice header total amount
//            invoiceHeader.setTotalAmount(BigDecimal.ZERO);
            //            invoiceHeader.setTotalAmount(new BigDecimal(0.0));
            document.setInitialAmounts();
//            document.setClaimAmount(new BigDecimal(0.0)); // Todo Vishal: Check changes

            itemDetailsViewModel.forEach(viewModel -> {
                if (viewModel.getId() != null) {
                    // If LineItemId is not null, it's an existing LineItem, Find the existing LineItem in the PO
                    invoiceHeader.getInvoiceItems()
                            .stream()
                            .filter(lineItem -> lineItem.getId().equals(viewModel.getId()))
                            .findFirst()
                            .ifPresent(lineItem -> addUpdateInvoiceItem(viewModel, lineItem, invoiceHeader, document, auth, false));
                } else {
                    invoiceHeader.getInvoiceItems()
                            .add(addUpdateInvoiceItem(viewModel, new InvoiceItem(), invoiceHeader, document, auth, true));
                }
            });
            documentRepository.saveAndFlush(document);
        }

    }

    private void addUpdateLineItemDetails(List<InvoiceItemDetailsViewModel> itemDetailsViewModel,
                                          InvoiceHeader invoiceHeader,
                                          IAuthContextViewModel auth,
                                          Document document) {

        if (invoiceHeader.getDocTypeId() == null) {
            logger.error("No Invoice doc type present for invoice ID: {}", invoiceHeader.getInvoiceHeaderId());
            throw new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
        }

        document.setInitialAmounts();

//        if (invoiceHeader.getHasPrecedingDocument()) {
//            handlePoBasedInvoice(itemDetailsViewModel, invoiceHeader, auth, document);
//        } else {
        handleNonPoBasedInvoice(itemDetailsViewModel, invoiceHeader, auth, document);
        //}

        documentRepository.saveAndFlush(document);
    }

    private void handlePoBasedInvoice(List<InvoiceItemDetailsViewModel> itemDetailsViewModel,
                                      InvoiceHeader invoiceHeader,
                                      IAuthContextViewModel auth,
                                      Document document) {
        try {
            logger.info("Processing PO-based invoice, ID: {}", invoiceHeader.getInvoiceHeaderId());

            deleteAllLineItems(invoiceHeader.getInvoiceHeaderId());
            invoiceHeader.getInvoiceItems().clear();
            invoiceHeaderRepository.saveAndFlush(invoiceHeader);

            if (itemDetailsViewModel == null || itemDetailsViewModel.isEmpty()) {
                logger.warn("No line items provided for Invoice ID: {}", invoiceHeader.getInvoiceHeaderId());
                return;
            }

            List<InvoiceItem> lineItems = itemDetailsViewModel.stream()
                    .map(viewModel -> addItemFromPurchaseOrderItem(viewModel, invoiceHeader, auth, document))
                    .collect(Collectors.toList());

            invoiceHeader.getInvoiceItems().addAll(lineItems);
            invoiceItemRepository.saveAllAndFlush(lineItems);

            logger.info("Successfully processed PO-based invoice, ID: {}", invoiceHeader.getInvoiceHeaderId());
        } catch (Exception e) {
            logger.error("Error processing PO-based invoice, ID: {}. Reason: {}", invoiceHeader.getInvoiceHeaderId(), e.getMessage(), e);
            throw new RuntimeException("Error processing PO-based invoice.", e);
        }
    }

    private void handleNonPoBasedInvoice(List<InvoiceItemDetailsViewModel> itemDetailsViewModel,
                                         InvoiceHeader invoiceHeader,
                                         IAuthContextViewModel auth,
                                         Document document) {
        try {
            logger.info("Processing Non-PO-based invoice, ID: {}", invoiceHeader.getInvoiceHeaderId());

            if (itemDetailsViewModel == null || itemDetailsViewModel.isEmpty()) {
                logger.warn("No line items provided for Invoice ID: {}", invoiceHeader.getInvoiceHeaderId());
                return;
            }

            Map<Long, InvoiceItem> existingItems = invoiceHeader.getInvoiceItems().stream()
                    .collect(Collectors.toMap(InvoiceItem::getId, Function.identity()));

            List<InvoiceItem> newItems = new ArrayList<>();

            for (InvoiceItemDetailsViewModel viewModel : itemDetailsViewModel) {
                if (viewModel.getId() != null && existingItems.containsKey(viewModel.getId())) {
                    addUpdateInvoiceItem(viewModel, existingItems.get(viewModel.getId()), invoiceHeader, document, auth, false);
                } else {
                    newItems.add(addUpdateInvoiceItem(viewModel, new InvoiceItem(), invoiceHeader, document, auth, true));
                }
            }

            invoiceHeader.getInvoiceItems().addAll(newItems);
            invoiceItemRepository.saveAllAndFlush(newItems); // Batch save new items

            logger.info("Successfully processed invoice, ID: {}", invoiceHeader.getInvoiceHeaderId());
        } catch (Exception e) {
            logger.error("Error processing invoice, ID: {}. Reason: {}", invoiceHeader.getInvoiceHeaderId(), e.getMessage(), e);
            throw new RuntimeException("Error processing invoice.", e);
        }
    }


    public void deleteAllLineItems(long invoiceHeaderId) {
        List<InvoiceItem> lineItems = invoiceItemRepository.findByInvoiceHeaderId(invoiceHeaderId);
        invoiceItemRepository.deleteAll(lineItems);
        invoiceItemRepository.flush();
    }

    public InvoiceItem addItemFromPurchaseOrderItem(InvoiceItemDetailsViewModel viewModel, InvoiceHeader invoiceHeader, IAuthContextViewModel auth, Document document) {
//        boolean isJsonBased = null == (null == invoiceHeader.getInvoiceReceived() ? null : invoiceHeader.getInvoiceReceived().getGovtJson()) ? false : true;

        InvoiceItem invoiceItem = new InvoiceItem();
        PurchaseOrderItem purchaseOrderItem = getPurchaseOrderItem(viewModel.getPoItemId(), auth.getCompanyCode());
        BeanUtils.copyProperties(viewModel, invoiceItem, "id"); // check id

        invoiceItem.setInvoiceHeader(invoiceHeader);
        invoiceItem.setInvoiceHeaderId(invoiceHeader.getInvoiceHeaderId());

        if (viewModel.getItemMasterId() != null) {
            ItemMaster item = getItemMaster(viewModel.getItemMasterId(), (int) auth.getCompanyCode(), invoiceHeader.getSellerId());
            invoiceItem.setType(item.getType());
            invoiceItem.setVendor(item.getVendor());
            invoiceItem.setVendorId(item.getVendorId());
        } else {
            // new case -- if item is not present in item master and considered as new item then set info and fetch vendor and set to item
            Company vendor = companyRepository.findByCamCompanyIdAndCompanyId(auth.getCompanyCode(), viewModel.getVendorId()).orElse(null);
            invoiceItem.setVendor(vendor);
            invoiceItem.setVendorId(viewModel.getVendorId().longValue());
            if (!StringUtils.isNullOrEmpty(viewModel.getType())) {
                invoiceItem.setType(ItemType.fromString(viewModel.getType()));
            }
        }

        // set Quantity = selectedQuantity - as for PO it will be same
        invoiceItem.setQuantity(viewModel.getSelectedQuantity());

        invoiceItem.setPurchaseOrderItem(purchaseOrderItem);
        invoiceItem.setPurchaseOrderItemId(purchaseOrderItem.getId());

        //purchaseOrderItem.setQuantity(customDocumentApprovalContainerRepository.getConsumedPRItemCount(purchaseRequestItem.getId(), auth.getCompanyCode()));
        invoiceItem.setTotal(viewModel.getUnitRate().multiply(BigDecimal.valueOf(invoiceItem.getQuantity())));
        BigDecimal assessableAmount = (invoiceItem.getTotal()).subtract(
                null == viewModel.getDiscount() ? BigDecimal.ZERO : viewModel.getDiscount());
        invoiceItem.setAssessableAmount(assessableAmount);
        invoiceItem.setTotal(assessableAmount);// amount without gst


        // calculate gst and add
        BigDecimal totalWithTax = BigDecimal.ZERO;
        if (null != viewModel.getTaxPercentage()) {
            // if tax then calculate tax on assessable and set to totalwithtax
            /**
             * 1. this total here is refereed as amountWithoutGst - differences in PO and invoice items are due to UI refused make changes in their generic model
             * 2. We do not store totalGstAmount on invoice as there is no provision due to discussions about not storing gst amount
             * calculating ond returning on run-time
             */
            totalWithTax = assessableAmount
                    .add(invoiceItem.getAssessableAmount().multiply(BigDecimal.valueOf(viewModel.getTaxPercentage() * 0.01)))
                    .add(calculateTotalWithTax(viewModel));
        } else {
            // else calculate on assessable - as assessable = total - discount if any
            totalWithTax = assessableAmount.add(calculateTotalWithTax(viewModel));
        }
        //calculate total add cost to InvoiceHeader

        invoiceItem.setTotalWithTax(totalWithTax);

//        BigDecimal totAmt = null == invoiceHeader.getTotalAmount() ? BigDecimal.ZERO : invoiceHeader.getTotalAmount();
//        invoiceHeader.setTotalAmount(totAmt.add(totalWithTax));

        BigDecimal totalAmount = null == document.getClaimAmount() ? BigDecimal.ZERO : document.getClaimAmount(); // Todo Vishal: Check changes
        document.setClaimAmount(totalAmount.add(totalWithTax)); // TODO: confirm ClaimAmount is with tax or without tax?


        if (viewModel.getBudgetNodeId() != null) {
            Budget budgetNode = getBudget(auth.getCompanyCode(), viewModel.getBudgetNodeId());
            invoiceItem.setBudgetNode(budgetNode);
            invoiceItem.setBudgetNodeId(viewModel.getBudgetNodeId());
        }

        invoiceItem.setGlMasterId(viewModel.getGlId());
        invoiceItem.setCreatedAt(LocalDateTime.now());
        return invoiceItem;
    }

    private BigDecimal calculateTotalWithTax(InvoiceItemDetailsViewModel viewModel) {
        BigDecimal cgst = null == viewModel.getCgst() ? BigDecimal.ZERO : viewModel.getCgst();
        BigDecimal igst = null == viewModel.getIgst() ? BigDecimal.ZERO : viewModel.getIgst();
        BigDecimal sgst = null == viewModel.getSgst() ? BigDecimal.ZERO : viewModel.getSgst();
        BigDecimal cessAmt = null == viewModel.getCessAmt() ? BigDecimal.ZERO : (viewModel.getCessAmt().multiply(BigDecimal.valueOf(null != viewModel.getSelectedQuantity() ? viewModel.getSelectedQuantity() : viewModel.getQuantity())));
        BigDecimal stateCessAmt = null == viewModel.getStateCessAmt() ? BigDecimal.ZERO : viewModel.getStateCessAmt().multiply(BigDecimal.valueOf(null != viewModel.getSelectedQuantity() ? viewModel.getSelectedQuantity() : viewModel.getQuantity()));
        BigDecimal cessNonAdvolAmt = null == viewModel.getCessNonAdvolAmount() ? BigDecimal.ZERO : viewModel.getCessNonAdvolAmount();
        BigDecimal cessStateNonAdvolAmt = null == viewModel.getStateCessNonAdvolAmount() ? BigDecimal.ZERO : viewModel.getStateCessNonAdvolAmount();
        BigDecimal otherCharges = null == viewModel.getOtherCharges() ? BigDecimal.ZERO : viewModel.getOtherCharges();
        return cgst.add(igst).add(sgst).add(cessAmt).add(stateCessAmt).add(cessNonAdvolAmt).add(cessStateNonAdvolAmt).add(otherCharges);
    }

    public InvoiceItem addUpdateInvoiceItem(InvoiceItemDetailsViewModel viewModel, InvoiceItem invoiceItem, InvoiceHeader invoiceHeader, Document document, IAuthContextViewModel auth, boolean isNew) {

        if (isNew) {
            BeanUtils.copyProperties(viewModel, invoiceItem, "id");

            if (viewModel.getItemMasterId() != null && viewModel.getItemMasterId() != 0) {
                ItemMaster item = getItemMaster(viewModel.getItemMasterId(), (int) auth.getCompanyCode(), invoiceHeader.getSellerId());
                invoiceItem.setType(item.getType());
                invoiceItem.setUnitRate(item.getRate());
                if (item.getSupplierRate() != null)
                    invoiceItem.setUnitRate(item.getSupplierRate());
                invoiceItem.setVendorId(item.getVendorId());
                invoiceItem.setVendor(item.getVendor());
            } else {
                // new case -- if item is not present in item master and considered as new item then set info and fetch vendor and set to item
                Company vendor = companyRepository.findByCamCompanyIdAndCompanyId(auth.getCompanyCode(), viewModel.getVendorId()).orElse(null);
                invoiceItem.setVendor(vendor);
                invoiceItem.setVendorId(viewModel.getVendorId().longValue());
                if (!StringUtils.isNullOrEmpty(viewModel.getType())) {
                    invoiceItem.setType(ItemType.fromString(viewModel.getType()));
                }
                invoiceItem.setUnitRate(viewModel.getUnitRate());
            }
        } else {
            // TODO : if item is edited then should we consider edited unit rate or rate from vendor
            BeanUtils.copyProperties(viewModel, invoiceItem, "id");
        }
        invoiceItem.setInvoiceHeader(invoiceHeader);
        invoiceItem.setInvoiceHeaderId(invoiceHeader.getInvoiceHeaderId());

        if (invoiceHeader.getHasPrecedingDocument()) {
            // for po based - UI sends quantity in selected quantity - overrides quantity
            invoiceItem.setQuantity(viewModel.getSelectedQuantity());
        } else {
            invoiceItem.setQuantity(viewModel.getQuantity());
        }
        invoiceItem.setTotal(invoiceItem.getUnitRate().multiply(BigDecimal.valueOf(invoiceItem.getQuantity())));
        BigDecimal assessableAmount = (invoiceItem.getTotal()).subtract(
                null == viewModel.getDiscount() ? BigDecimal.ZERO : viewModel.getDiscount());
        invoiceItem.setAssessableAmount(assessableAmount);
        invoiceItem.setTotal(assessableAmount);// amount without gst

        // calculate gst and add
        BigDecimal totalWithTax = BigDecimal.ZERO;
        if (null != viewModel.getTaxPercentage()) {
            // if tax then calculate tax on assessable and set to totalwithtax
            totalWithTax = assessableAmount.add(invoiceItem.getAssessableAmount().multiply(BigDecimal.valueOf(viewModel.getTaxPercentage() * 0.01))).add(calculateTotalWithTax(viewModel));
        } else {
            // else calculate on assessable - as assessable = total - discount if any
            totalWithTax = assessableAmount.add(calculateTotalWithTax(viewModel));
        }
        invoiceItem.setTotalWithTax(totalWithTax);

        BigDecimal totalAmount = null == document.getClaimAmount() ? BigDecimal.ZERO : document.getClaimAmount(); // Todo Vishal: Check changes
        document.setClaimAmount(totalAmount.add(totalWithTax));

        if (viewModel.getBudgetNodeId() != null) {
            Budget budgetNode = getBudget(auth.getCompanyCode(), viewModel.getBudgetNodeId());
            invoiceItem.setBudgetNode(budgetNode);
            invoiceItem.setBudgetNodeId(viewModel.getBudgetNodeId());
        }

        if (viewModel.getGlId() != null) {
            GlMaster glMaster = getGLMaster(viewModel.getGlId(), auth.getCompanyCode());
            invoiceItem.setGlMaster(glMaster);
            invoiceItem.setGlMasterId(glMaster.getId());
        }

        if (invoiceHeader.getHasPrecedingDocument()) {
            if (null != viewModel.getPoItemId()) {
                PurchaseOrderItem purchaseOrderItem = getPurchaseOrderItem(viewModel.getPoItemId(), auth.getCompanyCode());
                invoiceItem.setPurchaseOrderItem(purchaseOrderItem);
                invoiceItem.setPurchaseOrderItemId(purchaseOrderItem.getId());

                invoiceItem.setErpGrnId(
                        viewModel.getErpGrn() != null ? viewModel.getErpGrn().getId() : null
                );
            }
        }
        if (isNew) {
            invoiceItem.setCreatedAt(LocalDateTime.now());
        } else {
            invoiceItem.setUpdatedAt(LocalDateTime.now());
        }

//        invoiceItemRepository.saveAndFlush(invoiceItem);
        return invoiceItem;
    }

    private PurchaseOrderItem getPurchaseOrderItem(Long poItemId, long companyCode) {
        Optional<PurchaseOrderItem> poItem;

        poItem = purchaseOrderItemRepository.findById(poItemId);

        return poItem
                .orElseThrow(() -> {
                    logger.info("addItem: Could not find PurchaseOrder item with id: {} for company id: {}", poItemId, companyCode);
                    return new DomainInvariantException("PurchaseOrder: " + StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                });
    }

    private ItemMaster getItemMaster(Long itemMasterId, int companyCode, Integer vendorId) {
        return itemMasterRepository.findByIdAndCompanyId(itemMasterId, companyCode) // Todo: Convert CompanyId in itemMaster table to long
                .orElseThrow(() -> {
                    logger.info("addItem: Could not find item with id: {} with vendor id: {} for company id: {}", itemMasterId, vendorId, companyCode);
                    return new DomainInvariantException(StaticDataRegistry.PO_ITEM_CANNOT_BE_ADDED);
                });
    }

    private Budget getBudget(long companyCode, Long budgetNodeId) {
        return budgetRepository.findByCompanyCodeAndIdAndIsActiveTrue(companyCode, budgetNodeId).orElseThrow(() -> {
            logger.info("add budget to po item: Could not find with id: {} ", budgetNodeId);
            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
        });
    }

    private GlMaster getGLMaster(Long id, long companyCode) {
        return glMasterRepository.findByIdAndCompanyCode(id, companyCode).orElseThrow(() -> {
            logger.info("Could not find gl master with id: {} ", id);
            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
        });
    }

    private void prepareSellerDetails(InvoiceHeader invoiceHeader, InvoiceDTO govtInvJsonDto, Long camCompanyId) {
        String gst = Optional.ofNullable(govtInvJsonDto.getSellerDtls())
                .map(CompanyDtlsDTO::getGst).orElse("");

        if (!StringUtils.isEmpty(gst)) {
            List<Company> seller = companyRepository.findByCamCompanyIdAndGst(camCompanyId, gst);
            if (!seller.isEmpty()) {
                invoiceHeader.setSupplier(seller.get(0));
                invoiceHeader.setSellerId(seller.get(0).getCompanyId());
            }
        }
    }

    private void prepareBuyerDetails(InvoiceHeader invoiceHeader, InvoiceDTO invoiceJson, Long camCompanyId) {
        Optional.ofNullable(invoiceJson.getBuyerDtls())
                .map(CompanyDtlsDTO::getGst)
                .flatMap(gst -> gstinRepository.findByCompanyCodeAndGstinNo(camCompanyId, gst)).
                ifPresentOrElse(
                        gstinDetails -> {
                            invoiceHeader.setBuyerGstinDetails(gstinDetails, stateCodeRepo);
                            logger.info("GSTIN details found and set for GSTIN: {}", gstinDetails.getGstinNo());
                            // Add any other logic you need here
                        },
                        () -> logger.info("GSTIN details not found")
                );
    }

    private void createDocument(Document document, DocumentApprovalContainer documentApprovalContainer, DocumentSubgroup subgroup, String uploadType, IAuthContextViewModel auth) {
        document.setDocumentApprovalContainer(documentApprovalContainer);

//        if (!subgroup.isLocationRequired() && (subgroup.isSourceLocationApplicable() || subgroup.isDestinationLocationApplicable())) {
//            IEmployeeViewModel employeeDetails = employeeService.getEmployeeMasterData(auth.getUserEmail(), auth);
//            Location homeLocation = locationRepository.findByCompanyCode(auth.getCompanyCode()).stream().filter(l -> l.getLocation().equals(employeeDetails.getCityCode())).findFirst().orElseThrow(() -> new DomainInvariantException("Could not find the home location for this employee"));
//
//            document.setLocationCategory(homeLocation.getCategory());
//            document.setLocation(homeLocation.getLocation());
//        }

        document.setClaimAmount(new BigDecimal("0.0"));
        document.setInvoiceAmount(new BigDecimal("0.0"));
        document.setApplicableAmount(new BigDecimal("0.0"));
        document.setDocumentApprovalContainerId(documentApprovalContainer.getId());
        document.setDocumentSubgroup(subgroup);
        document.setDocumentSubgroupId(subgroup.getId());
        document.setFrequency(subgroup.getFrequency());
        document.setSource(uploadType);

        logger.info("create: Finding the applicable Expense Rule");
        DocumentRule applicableRule = getApplicableRule(document);

        logger.info("create: Attaching the applicable Expense Rule as id");
        document.setDocumentRuleId(applicableRule.getId());

        document.setCreatingUserId(auth.getUserId());
        document.setCreatedTimestamp(ZonedDateTime.now());
        document.setCompanyCode(auth.getCompanyCode());
        document.setEmployeeEmail(auth.getUserEmail());

        // setting documentDate on create
        document.setDocumentDate(LocalDate.now());

        // Bi-directional mapping
        documentApprovalContainer.getDocuments().add(document);
    }

    private void prepareDocumentFromJson(Document document, InvoiceDTO dto) {
        document.setTotalAmountGSTInclusive(Optional.ofNullable(dto.getValDtls()).map(ValDtlsDTO::getTotalAssValue).map(BigDecimal::new).orElse(BigDecimal.ZERO));
        document.setCgstAmount(Optional.ofNullable(dto.getValDtls()).map(ValDtlsDTO::getTotalCgstValue).map(BigDecimal::new).orElse(BigDecimal.ZERO));
        document.setIgstAmount(Optional.ofNullable(dto.getValDtls()).map(ValDtlsDTO::getTotalIgstValue).map(BigDecimal::new).orElse(BigDecimal.ZERO));
        document.setSgstAmount(Optional.ofNullable(dto.getValDtls()).map(ValDtlsDTO::getTotalSgstValue).map(BigDecimal::new).orElse(BigDecimal.ZERO));
        document.setTaxableAmount(Optional.ofNullable(dto.getValDtls()).map(ValDtlsDTO::getTotalAssValue).map(BigDecimal::new).orElse(BigDecimal.ZERO));

        document.setClaimAmount(Optional.ofNullable(dto.getValDtls()).map(ValDtlsDTO::getTotalInvoiceValue).map(BigDecimal::new).orElse(BigDecimal.ZERO));
        document.setClaimAmountInNativeCurrency(Optional.ofNullable(dto.getValDtls()).map(ValDtlsDTO::getTotalInvoiceValue).map(BigDecimal::new).orElse(BigDecimal.ZERO));

        document.setDocumentDate(DateTimeUtils.parseETLDateToLocalDate(Optional.ofNullable(dto.getDocDtls()).map(DocDtlsDTO::getDt).orElse(null)));
        document.setDocNo(Optional.ofNullable(dto.getDocDtls()).map(DocDtlsDTO::getNo).orElse(null));
    }

    private InvoiceDTO parseJsonFile(File jsonFile) throws IOException {
        InvoiceDeatilsByIrnDTO resp = objectMapper.readValue(jsonFile, InvoiceDeatilsByIrnDTO.class);
        if (null == resp || null == resp.getInvoiceJson()) {
            logger.error("Failed to parse the JSON, not readable");
            throw new DomainInvariantException("Invalid JSON Format, Please upload a valid JSON");
        }
        return resp.getInvoiceJson();
    }

    private void handleDocumentAmounts(Document entity, InvoiceDetailsReq invoiceDetailsRequest) {
        AmountDetails amountDetails = Optional.ofNullable(invoiceDetailsRequest.getAmountDetails()).orElse(new AmountDetails());

        entity.setTaxableAmount(defaultIfNull(amountDetails.getTotalAssValue()));
        entity.setTotalAmountGSTInclusive(defaultIfNull(amountDetails.getTotalInvoiceValue()));

        // Tax values
        entity.setCgstAmount(defaultIfNull(amountDetails.getTotalCgstValue()));
        entity.setIgstAmount(defaultIfNull(amountDetails.getTotalIgstValue()));
        entity.setSgstAmount(defaultIfNull(amountDetails.getTotalSgstValue()));

        // Total + tax
        entity.setClaimAmount(defaultIfNull(amountDetails.getTotalInvoiceValue()));
        entity.setClaimAmountInNativeCurrency(defaultIfNull(amountDetails.getFinalInvoiceValue()));
    }

    private BigDecimal defaultIfNull(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    private void initialiseDocument(Document entity, InvoiceDetailsReq invoiceDetailsRequest) {
        handleDocumentAmounts(entity, invoiceDetailsRequest);
        entity.setDocNo(Optional.ofNullable(invoiceDetailsRequest.getDocumentDetails()).map(InvoiceDocumentDetails::getDocNo).orElse(entity.getDocNo()));
    }

    private void prepareDocumentAndApprovalContainer(Document document, DocumentApprovalContainer approvalContainer, InvoiceDetailsReq invoiceDetailsRequest) {
        LocalDate documentDate = handleDocumentDates(invoiceDetailsRequest, document.getId());
        // Prepare Document
        prepareDocument(document, invoiceDetailsRequest, documentDate);

        // Prepare Document Approval Container
        prepareDocumentApprovalContainer(approvalContainer, documentDate);
    }

    private void prepareDocument(Document entity, InvoiceDetailsReq invoiceDetailsRequest, LocalDate documentDate) {
        handleDocumentAmounts(entity, invoiceDetailsRequest);
        entity.setDocumentDate(documentDate);
        entity.setStartDate(entity.getDocumentDate());
        entity.setEndDate(entity.getDocumentDate());

        entity.setDocNo(
                Optional.ofNullable(invoiceDetailsRequest.getDocumentDetails())
                        .map(InvoiceDocumentDetails::getDocNo)
                        .orElse(entity.getDocNo())
        );
    }

    private void prepareDocumentApprovalContainer(DocumentApprovalContainer entity, LocalDate startDate) {

        entity.setStartDate(startDate);
        entity.setEndDate(entity.getStartDate());
        entity.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());
    }

    private LocalDate handleDocumentDates(InvoiceDetailsReq invoiceDetailsRequest, Long entityId) {
        InvoiceDocumentDetails documentDetails = invoiceDetailsRequest.getDocumentDetails();

        if (documentDetails == null) {
            logger.error("handleDocumentDates: NULL document details in request for document ID: {}", entityId);
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_SAVE_FAILURE);
        }

        String invoiceDateString = documentDetails.getDocDate();
        if (invoiceDateString == null || invoiceDateString.isEmpty()) {
            logger.error("handleDocumentDates: Empty date in request for document ID: {}", entityId);
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_SAVE_FAILURE);
        }

        try {
            return DateTimeUtils.parseDatetoLocalDate(invoiceDateString);
        } catch (DateTimeParseException e) {
            logger.error("handleDocumentDates: Failed to parse date '{}' for entity ID: {}", invoiceDateString, entityId, e);
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_SAVE_FAILURE + " DateTimeParseException in handleDocumentDates");
        }
    }

    @Override
    public InvoiceApprovalContainerFromIRNViewModel getByIRN(MultipartFile invoice, IAuthContextViewModel auth) {
        QRResponseDTO qrResponseDTO = null;

        try {
            qrResponseDTO = lookForQRCode(invoice, auth);
        } catch (Exception e) {
            logger.error("There was an error processing the following JSON");
            return null;
        }

        if (!checkIfValidQRCodeFound(qrResponseDTO)) {
            return null;
        }


        Optional<InvoiceReceived> invoiceReceived = invoiceReceivedRepository.findByCompanyIdAndIrn(auth.getCompanyCode(),
                qrResponseDTO.getQrData().get("0").getIrn());

        if (invoiceReceived.isEmpty()) {
            logger.info("getByIRN: Invoice not found for irn {}", qrResponseDTO.getQrData().get("0").getIrn());
            return null;
        }


        logger.info("getByIRN: Invoice found with irn {}", invoiceReceived.get().getIrn());
        Document document = invoiceReceived.get().getInvoiceHeader().getDocument();
        return InvoiceApprovalContainerFromIRNViewModel
                .builder()
                .id(document.getDocumentApprovalContainerId())
                .subgroupFieldsJson(document.getDocumentSubgroup().getSubgroupFieldsJson())
                .build();
    }

    private InvoiceReceived getInvoiceByIRN(String irn, IAuthContextViewModel auth) {

        if (irn == null) {
            logger.info("getByIRN: IRN not found");
            return null;
        }
        List<ReportStatus> excludedStatuses = List.of(ReportStatus.CANCELLED, ReportStatus.DELETED, ReportStatus.DECLINED, ReportStatus.FAILED);
        Optional<InvoiceReceived> invoiceReceived = invoiceReceivedRepository.findByIrnAndInvoiceHeader_Document_DocumentApprovalContainer_ReportStatusNotInAndInvoiceHeader_Document_CompanyCode(
                irn, excludedStatuses, auth.getCompanyCode());

        if (invoiceReceived.isEmpty()) {
            logger.info("getByIRN: Invoice not found for irn {}", irn);
            return null;
        }
        return invoiceReceived.get();
    }

    private String getIRN(MultipartFile invoice, IAuthContextViewModel auth) throws IOException {
        QRResponseDTO qrResponseDTO = null;
        qrResponseDTO = lookForQRCode(invoice, auth);
        if (checkIfValidQRCodeFound(qrResponseDTO)) {
            return qrResponseDTO.getQrData().get("0").getIrn();
        }
        return null;
    }

    private String getIRN(String fileUrl, IAuthContextViewModel auth) throws IOException {
        if (fileUrl == null) return null;
        try {
            QRResponseDTO qrResponseDTO = lookForQRCodeUsingSignedUrl(fileUrl, auth);
            if (checkIfValidQRCodeFound(qrResponseDTO)) {
                return qrResponseDTO.getQrData().get("0").getIrn();
            }
        } catch (IOException e) {
            logger.warn("IOException occurred while fetching IRN: {}", e.getMessage());
        } catch (Exception e) {
            logger.warn("Unexpected error while fetching IRN: {}", e.getMessage());
        }
        return null;
    }

    @Override
    public InvoiceDetailsReq getById(Long invoiceHeaderId, IAuthContextViewModel auth) {

        InvoiceHeader invoiceHeader = invoiceHeaderRepository.findById(Long.valueOf(invoiceHeaderId)).get();
        Document document = documentRepository.findById(invoiceHeader.getDocument().getId()).get();
        DocumentApprovalContainer documentApprovalContainer = document.getDocumentApprovalContainer();
        BudgetSelectionViewModel budgetDetails = getBudgetNodeForDocument(document, auth);
        InvoiceDetailsReq invoiceDetails = InvoiceHeaderMapper.prepareInvoiceDetailsResponseData(invoiceHeader, document, documentApprovalContainer, budgetDetails);

        List<InvoiceItemDetailsViewModel> invoiceItems = invoiceHeader.getInvoiceItems().stream()
                .map(poItem -> invoiceItemToDto(poItem, auth))
                .collect(Collectors.toList());
        invoiceDetails.setItemList(invoiceItems);

        return invoiceDetails;
    }


    @Override
    public InvoiceDetailsReq getByDocumentContainerId(Long documentContainerId, IAuthContextViewModel auth) {
        // Initialise required variables
        Document document = null;
        InvoiceHeader invoiceHeader = null;
        BudgetSelectionViewModel budgetDetails = null;

        DocumentApprovalContainer documentApprovalContainer = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), documentContainerId)
                .orElseThrow(() -> {
                    logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer with id: {%s} could not be found.", documentContainerId));
                    return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_NOT_FOUND);
                });

        // Sanity check in case of any data inconsistency (if unrequired can be removed later)
        performApprovalContainerAndDocumentContainerSantityCheck(documentApprovalContainer, documentContainerId);

        // Get additional data for Invoice details
        document = getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(auth.getCompanyCode(), documentContainerId);
        budgetDetails = getBudgetNodeForDocument(document, auth);

        invoiceHeader = getInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(auth.getCompanyCode(), document);
        InvoiceDetailsReq response = InvoiceHeaderMapper.prepareInvoiceDetailsResponseData(invoiceHeader, document, documentApprovalContainer, budgetDetails);
        // handle dynamic field - business details

        if (documentApprovalContainer.getReportStatus() == ReportStatus.SUBMITTED || documentApprovalContainer.getReportStatus() == ReportStatus.ACCEPTED || documentApprovalContainer.getReportStatus() == ReportStatus.REVOKED) {
            response.setBusinessDetails(dynamicFormService.getBusinessDetailsFromJsonForStaticView(invoiceHeader.getDynamicFieldsJson(), auth));
        } else {
            response.setBusinessDetails(dynamicFormService.getBusinessDetailsFromJson(invoiceHeader.getDynamicFieldsJson()));
        }

        // handle items
        List<InvoiceItemDetailsViewModel> invoiceItems = invoiceHeader.getInvoiceItems().stream()
                .sorted(Comparator.comparing(InvoiceItem::getId)).map(poItem -> invoiceItemToDto(poItem, auth))
                .collect(Collectors.toList());
        response.setItemList(invoiceItems);
        // Todo : Confirm the below logic is correct.
        try {
            if (invoiceHeader.getInvoiceReceived() != null) {
                response.setDocUrl(gsFileIOProvider.downloadFileAndGetBase64(invoiceHeader.getInvoiceReceived().getDocumentUrl()));
            }
        } catch (IOException e) {
            throw new DomainInvariantException("Exception while downloadFileAndGetBase64 ");
        }
        return response;
    }

    @Override
    public InvoiceItemDetailsViewModel invoiceItemToDto(InvoiceItem invoiceItem, IAuthContextViewModel auth) {
        InvoiceItemDetailsViewModel dto = InvoiceItem.getViewModel(invoiceItem);

        // check if PR based PO - No PR items in case of NON-PR based PO
        boolean isPoBasedInvoice = null != invoiceItem.getPurchaseOrderItemId();
        if (isPoBasedInvoice) {
            // quantity in case of PO is PR available quantity
            ConsumedItemAvailableCountViewModel viewModel = customDocumentApprovalContainerRepository.getConsumedPOItemCount(invoiceItem.getPurchaseOrderItemId(), auth.getCompanyCode());
            // when consumed
            double availableQuantity = ((viewModel.getInitialQuantity() == null) ? 0.0 : viewModel.getInitialQuantity()) - viewModel.getQuantity();
//            dto.setQuantity((invoiceItem.getPurchaseOrderItem().getQuantity() - (double) quantity));
            // po count
            dto.setSelectedQuantity(dto.getQuantity());
            dto.setQuantity(Math.abs(availableQuantity));
            // set PR doc number as per UX
            dto.setDocNo(invoiceItem.getPurchaseOrderItem().getPurchaseOrderHeader().getDocument().getDocNo());

            dto.setCreatedAt(DateTimeUtils.formatDateJPQL(invoiceItem.getPurchaseOrderItem().getCreatedAt()));
            dto.setCreatedBy(invoiceItem.getPurchaseOrderItem().getPurchaseOrderHeader().getCreatedBy().getFirstName() + " " +
                    invoiceItem.getPurchaseOrderItem().getPurchaseOrderHeader().getCreatedBy().getLastName());

            ERPGrnViewModel erpGrnViewModel = POGrnMapping.getViewModel(invoiceItem.getErpGrn());
            dto.setErpGrn(erpGrnViewModel);
        }
        return dto;
    }

    private BudgetSelectionViewModel getBudgetNodeForDocument(Document document, IAuthContextViewModel auth) {
        // Todo This needs to be converted to a api call.
        Long budgetId = document.getDocumentSubgroup().getBudgetId();
        return budgetServiceImplementation.getBudgetNode(budgetId, auth);
    }

    // Sanity check in case of any data inconsitency (if unrequired can be removed later)
    private void performApprovalContainerAndDocumentContainerSantityCheck(DocumentApprovalContainer documentApprovalContainer, Long documentContainerId) {
        // Sanity check in case of any data inconsitency (if unrequired can be removed later)
        if (!documentApprovalContainer.getId().equals(documentContainerId)) {
            logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer sanity check failed. The provided id in the query : {%s} and the resulting id in the object:  {%s} are mismatched.", documentContainerId, documentApprovalContainer.getId()));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_NOT_FOUND);
        }
    }

    @Override
    public Document getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(long companyCode, Long documentContainerId) {
        List<Document> attachedDocumentsArray = documentRepository.findAllByCompanyCodeAndDocumentApprovalContainerId(companyCode, documentContainerId);

        // Invoices can only have a single document attached to the document container
        if (attachedDocumentsArray.size() != 1) {
            logger.error(String.format("getByDocumentContainerId: DocumentApprovalContainer with id: {%s} has multiple documents attached to it.", documentContainerId));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_NOT_FOUND);
        }

        return attachedDocumentsArray.get(0);
    }

    @Override
    public String getInvoicePDFBase64ByDocumentIdentifier(String documentIdentifier, IAuthContextViewModel auth) {
        logger.info("getInvoicePDFBase64ByDocumentIdentifier: Started for documentIdentifier: {} and CompanyCode: {}", documentIdentifier, auth.getCompanyCode());

        // Single Document Identifier Validations
        List<DocumentApprovalContainer> documentApprovalContainerList = reportRepository
                .findByCompanyCodeAndDocumentIdentifier(auth.getCompanyCode(), documentIdentifier);

        // Reuse the utility method
        DocumentApprovalContainer documentApprovalContainer = CommonUtility.getSingleRecordOrThrow(
                documentApprovalContainerList,
                "No document found for identifier %s in company %s",
                "Multiple documents found for identifier %s in company %s",
                documentIdentifier,
                auth.getCompanyCode()
        );

        Document document = getDocumentFromDocumentApprovalContainerWithInvoiceUniquenessChecks(auth.getCompanyCode(), documentApprovalContainer.getId());

        logger.info("getting invoice");
        InvoiceHeader invoiceHeader = getInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(auth.getCompanyCode(), document);

        try {
            logger.info("returning base64 pdf");
            if (invoiceHeader.getInvoiceReceived() != null) {
                return gsFileIOProvider.downloadFileAndGetBase64(invoiceHeader.getInvoiceReceived().getDocumentUrl());
            }
        } catch (IOException e) {
            logger.error("Exception occurred while getting base64 pdf for invoice {} ", invoiceHeader.getInvoiceHeaderId());
            throw new DomainInvariantException("Exception while downloadFileAndGetBase64 ");
        }
        return null;
    }

    @Override
    public SignedUrlResponseViewModel createSignedUrl(GetSignedUrlRequestViewModel request, IAuthContextViewModel auth) {
        try {
            List<SignedUrlResponseData> signedUrlResponseDataList = new ArrayList<>();

            for (GetSignedUrlRequestViewModel.SignedUrlRequestData data : request.getData()) {

                String fileName = UUID.randomUUID() + "_" + data.getName();

                // Build file path
                String filePath = uploadEnv + StaticDataRegistry.URL_SEPERATOR +
                        "TEMP_FILE" + StaticDataRegistry.URL_SEPERATOR +
                        auth.getCompanyCode() + StaticDataRegistry.URL_SEPERATOR +
                        fileName;

                // Generate Signed URL
                String signedUrl = gcpCSFileIOProvider.createSignedURL(filePath, data.getContentType(), com.google.cloud.storage.HttpMethod.PUT);

                // Construct response data
                SignedUrlResponseData signedUrlResponseData = new SignedUrlResponseData();
                signedUrlResponseData.setId(fileName);
                signedUrlResponseData.setSignedUrls(signedUrl);
                signedUrlResponseData.setName(data.getName());
                signedUrlResponseData.setFilePath(filePath);
                signedUrlResponseData.setIsUpload(false);

                signedUrlResponseDataList.add(signedUrlResponseData);
            }

            return new SignedUrlResponseViewModel(signedUrlResponseDataList);

        } catch (Exception e) {
            logger.error("createSignedUrl: Failed to generate signed URLs", e);
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_GENERATE_SIGNED_URL);
        }
    }

    @Override
    public BulkInvoiceUploadResponseViewModel invoiceBulkUpload(
            BulkInvoiceUploadRequestViewModel requestViewModel,
            IAuthContextViewModel auth) {

        logger.info("START: invoiceBulkUpload | Type: {}, Group: {}, SubGroup: {}, Items: {}",
                requestViewModel.getType(),
                requestViewModel.getGroup(),
                requestViewModel.getSubGroup(),
                requestViewModel.getData() != null ? requestViewModel.getData().size() : 0);

        if (requestViewModel.getData() == null || requestViewModel.getData().isEmpty()) {
            logger.warn("Bulk invoice upload request contains no data.");
            BulkInvoiceUploadResponseViewModel errorResponse = buildErrorResponse("Bulk invoice upload request contains no data.");
            logger.info("END: invoiceBulkUpload | Result: failure due to empty data");
            return errorResponse;
        }

        List<SignedUrlResponseData> responseDataList = new ArrayList<>();
        boolean allSuccess = true;

        for (SignedUrlResponseData requestData : requestViewModel.getData()) {
            String uuid = UUID.randomUUID().toString();

            logger.info("START: process invoice | UUID: {}, FileName: {}", uuid, requestData.getName());

            try {
                boolean uploadSuccess = processSingleInvoiceUpload(requestViewModel, requestData, auth, uuid);
                requestData.setIsUpload(uploadSuccess);

                if (!uploadSuccess) {
                    logger.warn("Invoice upload failed | UUID: {}, FileName: {}", uuid, requestData.getName());
                    allSuccess = false;
                } else {
                    logger.info("Invoice upload succeeded | UUID: {}, FileName: {}", uuid, requestData.getName());
                }

            } catch (Exception ex) {
                logger.error("Exception during invoice upload | UUID: {}, Message: {}", uuid, ex.getMessage(), ex);
                requestData.setIsUpload(false);
                allSuccess = false;
            }

            responseDataList.add(requestData);
            logger.info("END: process invoice | UUID: {}, UploadSuccess: {}", uuid, requestData.getIsUpload());
        }

        String resultMessage = allSuccess
                ? "Invoice uploaded successfully"
                : "Some invoices failed to upload";

        BulkInvoiceUploadResponseViewModel response = buildResponse(allSuccess, resultMessage, responseDataList);

        logger.info("END: invoiceBulkUpload | Success: {}, Message: {}", allSuccess, resultMessage);
        return response;
    }

    private boolean processSingleInvoiceUpload(
            BulkInvoiceUploadRequestViewModel requestViewModel,
            SignedUrlResponseData requestData,
            IAuthContextViewModel auth,
            String uuid) {

        logger.debug("START: processSingleInvoiceUpload | UUID: {}", uuid);

        Map<String, String> attributesMap = Map.of(
                "dataType", "Bulk_Invoice_Upload",
                "sourceId", environmentDataProvider.getProductId(),
                "requestId", uuid
        );

        QueueBulkInvoiceUploadDataViewModel queueData = new QueueBulkInvoiceUploadDataViewModel(
                requestViewModel.getType(),
                requestViewModel.getGroup(),
                requestViewModel.getSubGroup(),
                requestData,
                auth.getToken()
        );

        String messageJson = CommonUtility.serializeMessage(queueData, uuid);
        ByteString data = ByteString.copyFromUtf8(messageJson);

        PubsubMessage pubsubMessage = PubsubMessage.newBuilder()
                .setData(data)
                .putAllAttributes(attributesMap)
                .build();

        logger.debug("Serialized message for PubSub | UUID: {}", uuid);
        String status = pubSubPublisherService.publishMessage(uuid, "Bulk_Invoice_Upload", pubsubMessage);
        logger.debug("PubSub status received | UUID: {}, Status: {}", uuid, status);

        boolean success = status != null && !status.toLowerCase().contains("error");

        logger.debug("END: processSingleInvoiceUpload | UUID: {}, Success: {}", uuid, success);
        return success;
    }

    private BulkInvoiceUploadResponseViewModel buildErrorResponse(String message) {
        logger.debug("START: buildErrorResponse | Message: {}", message);
        BulkInvoiceUploadResponseViewModel response = BulkInvoiceUploadResponseViewModel.builder()
                .success(false)
                .message(message)
                .build();
        logger.debug("END: buildErrorResponse");
        return response;
    }

    private BulkInvoiceUploadResponseViewModel buildResponse(
            boolean success,
            String message,
            List<SignedUrlResponseData> dataList) {

        logger.debug("START: buildResponse | Success: {}, Message: {}, Items: {}", success, message, dataList.size());
        BulkInvoiceUploadResponseViewModel response = BulkInvoiceUploadResponseViewModel.builder()
                .success(success)
                .message(message)
                .data(dataList)
                .build();
        logger.debug("END: buildResponse");
        return response;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public DocumentCreateResponse createInvoicePostUpload(QueueBulkInvoiceUploadDataViewModel invoiceUploadDetailsRequest, IAuthContextViewModel auth) {

        String requestId = invoiceUploadDetailsRequest.getRequestId() == null ? UUID.randomUUID().toString() : invoiceUploadDetailsRequest.getRequestId();
        logger.info("START createInvoicePostUpload | RequestId: {}", requestId);

        DocumentCreateResponse response = new DocumentCreateResponse();

        try {
            Long metadataId = invoiceUploadDetailsRequest.getGroup();
            Long subgroupId = invoiceUploadDetailsRequest.getSubGroup();

            // File upload
            String filename = null;
            URL signedUrl = null;

            try {
                logger.info("Moving file to final location | RequestId: {}", requestId);
                filename = fileIOService.handleMoveInvoice(invoiceUploadDetailsRequest.getFilePath(), invoiceUploadDetailsRequest.getName(), auth.getCompanyCode());
                signedUrl = fileIOService.getSignedUrl(filename);
                logger.info("File moved and signed URL generated | RequestId: {}, File: {}", requestId, filename);
            } catch (Exception e) {
                logger.error("File move failed | RequestId: {} | Error: {}", requestId, e.getMessage(), e);
                throw new RuntimeException("File upload failed. Please try again.", e);
            }

            String irn = null;
            InvoiceReceived invoiceReceived = null;
            try {

                logger.info("Reading IRN from QR Code | RequestId: {}", requestId);
                irn = getIRN(signedUrl.toString(), auth);
                invoiceReceived = getInvoiceByIRN(irn, auth);

                // Check if invoice with same IRN already exists
                if (invoiceReceived != null) {
                    logger.info("Existing invoice found with IRN: {} | RequestId: {}", irn, requestId);
                    boolean isEligibleToReprocess = handleExistingInvoice(requestId, invoiceReceived, invoiceUploadDetailsRequest, auth, irn);
                    if (!isEligibleToReprocess) {
                        DocumentApprovalContainer dac = invoiceReceived.getInvoiceHeader().getDocument().getDocumentApprovalContainer();
                        response.setContainerId(dac.getId());
                        response.setMessage("Duplicate IRN - Invoice already available with DocumentIdentifier : " + dac.getDocumentIdentifier());
                        return response;
                    }
                }
            } catch (IOException e) {
                logger.warn("QR Code reading failed due to IOException | RequestId: {} | Error: {}", requestId, e.getMessage());
            } catch (Exception e) {
                logger.warn("QR Code reading encountered error | RequestId: {} | Error: {}", requestId, e.getMessage());
            }

            // Create DocumentApprovalContainer
            logger.info("Creating DocumentApprovalContainer | RequestId: {}", requestId);
            DocumentApprovalContainerViewModel viewModel = documentApprovalContainerUserService.create(metadataId, auth);
            DocumentApprovalContainer documentApprovalContainer = getDocumentApprovalContainer(requestId, viewModel.getId(), auth.getCompanyCode());

            validateParentExpense(documentApprovalContainer);
            DocumentSubgroup subgroup = getSubgroup(subgroupId, auth.getCompanyCode());

            if (subgroup.isFrozen()) {
                logger.warn("Subgroup is frozen | RequestId: {}", requestId);
                throw new DomainInvariantException("Subgroup is frozen");
            }

            logger.info("Creating new Invoice Document entity | RequestId: {}", requestId);
            Document document = new Document();
            createDocument(document, documentApprovalContainer, subgroup, StaticDataRegistry.LOOKUP_VALUE_PDF, auth);

            // Create invoice-related entities
            InvoiceHeader invoiceHeader = createInvoiceHeader(document, subgroup, auth);
            invoiceReceived = createInvoiceReceived(invoiceHeader, filename, invoiceUploadDetailsRequest.getName(), irn, auth.getCompanyCode());

            // doc approval entities
            subgroup.getDocuments().add(document);
            invoiceHeader.setInvoiceReceived(invoiceReceived);

            response.setContainerId(documentApprovalContainer.getId());

            //Create entities // Save all entities in a single transaction
            saveEntities(document, documentApprovalContainer, invoiceHeader, invoiceReceived);
            logger.info("Entities saved successfully | RequestId: {}", requestId);

            // Mailroom or OCR Processing
            Optional<Mailroom> optionalMailroom = mailRoomReposiitory.findByCompanyCodeAndIrn(auth.getCompanyCode(), irn);
            if (optionalMailroom.isPresent()) {
                logger.info("Mailroom match found | RequestId: {}", requestId);
                // set default status to processing
                documentApprovalContainer.setReportStatus(ReportStatus.DRAFT);
                processMailroom(requestId, optionalMailroom.get(), invoiceReceived, invoiceHeader, document, documentApprovalContainer, auth);
            } else if (signedUrl != null) {
                logger.info("Uploading invoice to DMR | RequestId: {}", requestId);
                // set default status to processing
                documentApprovalContainer.setReportStatus(ReportStatus.PROCESSING);
                idmrService.uploadMultipartFileToIPU(signedUrl.toString(), invoiceReceived.getInvoiceReceivedId(),
                        documentApprovalContainer.getDocumentIdentifier(), auth);
            }

        } catch (Exception e) {
            logger.error("Exception occurred during invoice post upload | RequestId: {} | Error: {}", requestId, e.getMessage(), e);
            throw new RuntimeException("Failed to process invoice", e);
        } finally {
            deleteTempFile(requestId, invoiceUploadDetailsRequest.getFilePath());
            logger.info("Temporary file deleted | RequestId: {}", requestId);
            logger.info("END createInvoicePostUpload | RequestId: {}", requestId);
        }

        return response;
    }

    private void processMailroom(String requestId,
                                 Mailroom mailroom, InvoiceReceived invoiceReceived, InvoiceHeader invoiceHeader,
                                 Document document, DocumentApprovalContainer documentApprovalContainer, IAuthContextViewModel auth) throws JsonProcessingException, InvalidInputException, JsonProcessingException {

        logger.info("START processMailroom | IRN: {} | RequestId: {}", mailroom.getIrn(), requestId);
        InvoiceDTO invoiceJson = objectMapper.readValue(mailroom.getJsonData(), InvoiceDTO.class);

        List<InvoiceItem> oldInvoiceItems = invoiceHeader.getInvoiceItems();
        if (oldInvoiceItems != null && !oldInvoiceItems.isEmpty()) {
            logger.info("Deleting old invoice items | RequestId: {}", requestId);
            invoiceItemRepository.deleteAll(oldInvoiceItems);
        }

        // Prepare invoice details
        logger.info("Preparing invoice details | RequestId: {}", requestId);
        prepareSellerDetails(invoiceHeader, invoiceJson, auth.getCompanyCode());
        prepareBuyerDetails(invoiceHeader, invoiceJson, auth.getCompanyCode());

        logger.info("Mapping invoice header and document from JSON | RequestId: {}", requestId);
        InvoiceHeaderMapper.prepareInvoiceHeaderFromJson(invoiceHeader, invoiceJson, document);

        handleLookupData(invoiceHeader, invoiceJson);

        prepareDocumentFromJson(document, invoiceJson);

        logger.info("Mapping invoice received from JSON | RequestId: {}", requestId);
        InvoiceHeaderMapper.prepareInvoiceReceivedFromJson(invoiceReceived, invoiceJson, auth.getCompanyCode(), invoiceHeader);
        invoiceReceived.setGovtJson(objectMapper.writeValueAsString(invoiceJson));
        invoiceReceived.setJsonSource(JsonSource.GOV_JSON);

        documentApprovalContainer.getDocuments().add(document);

        // Save entities to update consumed flag
        logger.info("Updating mailroom as consumed | RequestId: {}", requestId);
        mailroom.setConsumed(true);
        mailRoomReposiitory.saveAndFlush(mailroom);

        //Update entities after govt json data mapping.
        logger.info("Saving entities post-mapping json mail room | RequestId: {}", requestId);
        saveEntities(document, documentApprovalContainer, invoiceHeader, invoiceReceived);

        logger.info("END processMailroom | RequestId: {}", requestId);

    }

    private void deleteTempFile(String requestId, String filePath) {
        logger.info("START deleteTempFile | RequestId: {} | FilePath: {}", requestId, filePath);

        try {
            gcpCSFileIOProvider.deleteFileFromTempStorage(filePath);
            logger.info("Temp file deleted successfully | RequestId: {}", requestId);
        } catch (Exception e) {
            logger.warn("Failed to delete temp file | RequestId: {} | Error: {}", requestId, e.getMessage(), e);
        }

        logger.info("END deleteTempFile | RequestId: {}", requestId);
    }

    private boolean handleExistingInvoice(String requestId, InvoiceReceived invoiceReceived,
                                          QueueBulkInvoiceUploadDataViewModel invoiceUploadDetailsRequest,
                                          IAuthContextViewModel auth,
                                          String irn) throws IOException {

        logger.info("START handleExistingInvoice | RequestId: {} | IRN: {}", requestId, irn);

        DocumentApprovalContainer dac = invoiceReceived.getInvoiceHeader().getDocument().getDocumentApprovalContainer();

        // this check is for P2P- 1635 - erp sync - re-upload
        // Check if the invoice is not in a cancellable state
        if (dac.getReportStatus() != ReportStatus.CANCELLED &&
                dac.getReportStatus() != ReportStatus.DELETED &&
                dac.getReportStatus() != ReportStatus.FAILED) {

            // If JSON is missing or JSON-PDF match is already established, throw an error - TODO: confirm removing this snippet with Vedant
//            if (invoiceReceived.getGovtJson() == null || invoiceReceived.getJsonPdfMatchedStatus() != null) {
//                logger.info("Duplicate IRN - Invoice PDF already available | RequestId: {}", requestId);
//                throw new DomainInvariantException("Duplicate IRN - Invoice PDF already available");
//            }
            // TODO : delete file
            // upload invoice and set url against uploaded json
//            logger.info("Handling file move for duplicate invoice | RequestId: {}", requestId);
//            String filename = fileIOService.handleMoveInvoice(
//                    invoiceUploadDetailsRequest.getFilePath(),
//                    invoiceUploadDetailsRequest.getName(),
//                    auth.getCompanyCode()
//            );

            // Update invoice details
//            invoiceReceived.setDocumentUrl(filename);
//            invoiceReceived.setJsonPdfMatchedStatus(
//                    lookupRepository.findByLookupDataCode(UUID.fromString(StaticDataRegistry.LOOKUP_CODE_JSON_MATCH))
//            );
//            invoiceReceived.setUpdatedAt(DateTimeUtils.getCurrentTimestamp());
            dac.setUpdatedTimestamp(DateTimeUtils.getCurrentZonedDateTime());

            invoiceReceivedRepository.saveAndFlush(invoiceReceived);
            reportRepository.saveAndFlush(dac);
            logger.info("Duplicate IRN - Invoice already available | RequestId: {}", requestId);
            return false;
            // call to IPU will not happen for OCR as we already have json available
//            response.setMessage("Match found, PDF is linked with existing Json with IRN: " + irn + " Request Id : " + requestId);

        }
        logger.info("END handleExistingInvoice | RequestId: {}", requestId);
        return true;
    }

    private LookupData getLookupData(String type, String value, String potentialErrorMessage) {
        return lookupRepository.findByTypeAndValue(type, value)
                .orElseThrow(() -> {
                    logger.error("Lookup error: Type={} Value={}", type, value);
                    return new RecordNotFoundException(potentialErrorMessage);
                });
    }

    private DocumentApprovalContainer getDocumentApprovalContainer(String requestId, Long id, Long companyCode) {
        logger.info("START getDocumentApprovalContainer | RequestId: {} | CompanyCode: {} | ContainerId: {}", requestId, companyCode, id);

        DocumentApprovalContainer container = reportRepository.findByCompanyCodeAndId(companyCode, id)
                .orElseThrow(() -> {
                    String errorMsg = String.format("Could not find documentApprovalContainer with id: %d | CompanyCode: %d | RequestId: %s", id, companyCode, requestId);
                    logger.error(errorMsg);
                    return new RecordNotFoundException(errorMsg);
                });

        logger.info("END getDocumentApprovalContainer | RequestId: {} | Found ContainerId: {}", requestId, container.getId());
        return container;
    }

    private void validateParentExpense(DocumentApprovalContainer documentApprovalContainer) {
        if (!isParentExpenseReportSubmittable(documentApprovalContainer)) {
            throw new DomainInvariantException("Parent expense documentApprovalContainer is neither in draft nor sent-back state; hence not submittable");
        }
    }

    private DocumentSubgroup getSubgroup(Long subgroupId, Long companyCode) {
        return subgroupRepository.findByCompanyCodeAndId(companyCode, subgroupId)
                .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find subgroup with id: %d", subgroupId)));
    }

    private InvoiceHeader createInvoiceHeader(Document document, DocumentSubgroup subgroup, IAuthContextViewModel auth) {
        InvoiceHeader invoiceHeader = new InvoiceHeader();
        invoiceHeader.setDocTypeId(DocumentType.getTypeId(DocumentType.INVOICE)); // Invoice
        invoiceHeader.setCreatingUserId(auth.getUserId());
        invoiceHeader.setDocument(document);
        invoiceHeader.setHasPrecedingDocument(subgroup.getHasPrecedingDocument());
        return invoiceHeader;
    }

    private InvoiceReceived createInvoiceReceived(InvoiceHeader invoiceHeader, String filename, String originalFileName, String irn, Long companyCode) {
        return InvoiceHeaderMapper.prepareInvoiceReceived(new InvoiceDetailsReq(), companyCode, invoiceHeader, filename, irn);
    }

    private void saveEntities(Document document, DocumentApprovalContainer documentApprovalContainer,
                              InvoiceHeader invoiceHeader, InvoiceReceived invoiceReceived) {

        reportRepository.saveAndFlush(documentApprovalContainer);
        documentRepository.saveAndFlush(document);
        invoiceHeaderRepository.save(invoiceHeader);
        invoiceReceivedRepository.save(invoiceReceived);

        invoiceReceivedRepository.flush();
        invoiceHeaderRepository.flush();

    }


    @Override
    public InvoiceHeader getInvoiceHeaderFromDocumentWithInvoiceInvoiceUniquenessChecks(long companyCode, Document document) {
        return invoiceHeaderRepository.findByDocumentId(document.getId()).orElseThrow(() -> {
            logger.error(String.format("getByDocumentContainerId: Document with id: {%s} has not got an invoice attached to it.", document.getId()));
            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_NOT_FOUND);
        });
    }

    @Override
    public void delete(long id, IAuthContextViewModel auth) {

//        InvoiceHeader invoice = invoiceHeaderRepository.findByDocumentId(id).orElseThrow(() -> {
//            logger.error(String.format("getByInvoiceId: id: {%s} has not got an invoice attached to it.", id));
//            return new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_NOT_FOUND);
//        });
//        DocumentApprovalContainer documentContainer = invoice.getDocument().getDocumentApprovalContainer();
//        if (!canDeleteParentExpenseReport(documentContainer)) {
//            throw new DomainInvariantException("Parent expense report is in draft or sent-back state; hence not submittable");
//        }
//

//        Document document = invoice.getDocument();
//        if (documentContainer.getId() != null) {
//
//            // Delete InvoiceLineItems associated with the Invoice
//            if (invoice.getLineItemList() != null) {
//                invoice.getLineItemList().stream().forEach(lineItemDetailsRepository::delete);
//            }
//            if (invoice.getInvoiceReceived() != null) {
//                invoiceReceivedRepository.delete(invoice.getInvoiceReceived());
//            }
//
//            invoiceHeaderRepository.delete(invoice);
//
//            if (document != null) {
//                documentRepository.delete(document);
//            }
//            // Finally, delete the DocumentContainer
//            reportRepository.delete(documentContainer);
//        }

    }

    @Override
    @Transactional
    public boolean saveInvoiceBuyerDocIdStatusUpdate(StatusUpdateViewModel statusUpdateViewModel, IAuthContextViewModel auth) {
        logger.info("Inside saveInvoiceBuyerDocIdStatusUpdate" + DateTimeUtils.getCurrentZonedDateTime());
        try {
            Optional<InvoiceReceived> invoiceReceivedOptional = invoiceReceivedRepository.findByDmrReferenceId(statusUpdateViewModel.getRequestId());
            if (invoiceReceivedOptional.isEmpty()) {
                invoiceReceivedOptional = invoiceReceivedRepository.findByDocumentIdentifierId(statusUpdateViewModel.getRequestId());
            }
            if (invoiceReceivedOptional.isPresent()) {

                InvoiceReceived invoiceReceived = invoiceReceivedOptional.get();

                InvoiceHeader invoiceHeader = invoiceReceived.getInvoiceHeader();
                Document document = invoiceHeader.getDocument();
                DocumentApprovalContainer documentApprovalContainer = document.getDocumentApprovalContainer();

                documentApprovalContainer.setReportStatus(ReportStatus.PROCESSING);

                invoiceReceivedRepository.saveAndFlush(invoiceReceived);
                reportRepository.saveAndFlush(documentApprovalContainer);

                return true;
            }
        } catch (Exception exception) {
            logger.error("Failed to update status. Message :" + exception.getMessage());
            return false;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean saveInvoiceBuyerDocId(BuyerDocIdRequestViewModel buyerDocIdRequestViewModel, IAuthContextViewModel auth) {

        try {
            Optional<InvoiceReceived> invoiceReceivedOptional = invoiceReceivedRepository.findById(buyerDocIdRequestViewModel.getInvoiceReceivedId());

            if (invoiceReceivedOptional.isPresent()) {
                InvoiceReceived invoiceReceived = invoiceReceivedOptional.get();
                if (buyerDocIdRequestViewModel.isHasSubscription()) {
                    invoiceReceived.setDmrReferenceId(buyerDocIdRequestViewModel.getBuyerDocId());
                    invoiceReceivedRepository.saveAndFlush(invoiceReceived);
                } else {
                    InvoiceHeader invoiceHeader = invoiceReceived.getInvoiceHeader();
                    Document document = invoiceHeader.getDocument();
                    DocumentApprovalContainer documentApprovalContainer = document.getDocumentApprovalContainer();
                    documentApprovalContainer.setReportStatus(ReportStatus.DRAFT);

                    reportRepository.saveAndFlush(documentApprovalContainer);

                }
                return true;
            }
        } catch (Exception e) {
            logger.info("Failed to save BuyerDocId", e);
        }
        return false;
    }

    @Override
    @Transactional
    public boolean saveInvoiceOCRJson(InvoiceDeatilsByIrnDTO invoiceDeatilsByIrnDTO, IAuthContextViewModel auth) {
        String requestId = invoiceDeatilsByIrnDTO.getCustomDetails().getRequestId();
        DocumentApprovalContainer documentApprovalContainer = null;
        try {
            logger.info("START saveInvoiceOCRJson | RequestId: {} | CompanyCode: {}", requestId, auth.getCompanyCode());
            logger.debug("Incoming Invoice Data: {}", invoiceDeatilsByIrnDTO);

            boolean isOcrDone = "SUCCESS".equalsIgnoreCase(invoiceDeatilsByIrnDTO.getCustomDetails().getOcrStatus());
            String ocrStatus = Optional.ofNullable(invoiceDeatilsByIrnDTO.getCustomDetails().getOcrStatus()).orElse(StringConstants.EMPTY);

            if (StaticDataRegistry.isNullOrEmptyOrWhitespace(requestId)) {
                logger.error("Missing requestId in customDetails | CompanyCode: {}", auth.getCompanyCode());
                throw new RuntimeException("Invoice requestId (reference_id) is not provided in custom details");
            }
            logger.info("Looking for InvoiceReceived | RequestId: {} | CompanyCode: {}", requestId, auth.getCompanyCode());
            Optional<InvoiceReceived> invoiceReceivedOptional = invoiceReceivedRepository.findByDocumentIdentifierIdAndCompanyId(
                    requestId, auth.getCompanyCode()
            );
            if (invoiceReceivedOptional.isEmpty()) { // search by dmr reference id for older records
                invoiceReceivedOptional = invoiceReceivedRepository.findByDmrReferenceId(requestId);
            }
            if (invoiceReceivedOptional.isEmpty()) {
                logger.error("InvoiceReceived not found | RequestId: {} | CompanyCode: {}", requestId, auth.getCompanyCode());
                return false;
            }

            logger.info("InvoiceReceived found | RequestId: {}", requestId);
            InvoiceReceived invoiceReceived = invoiceReceivedOptional.get();
            InvoiceHeader invoiceHeader = invoiceReceived.getInvoiceHeader();
            Document document = invoiceHeader.getDocument();

            InvoiceDTO invoiceJson = invoiceDeatilsByIrnDTO.getInvoiceJson();

            Optional<Mailroom> optionalMailroom = Optional.empty();

            if (invoiceJson.getIrn() != null) {
                optionalMailroom = mailRoomReposiitory.findByCompanyCodeAndIrn(auth.getCompanyCode(), invoiceJson.getIrn());
            }
            // Mailroom or OCR Processing

            if (optionalMailroom.isPresent()) {
                logger.info("Mailroom match found | RequestId: {}", requestId);
                // set default status to processing
                documentApprovalContainer.setReportStatus(ReportStatus.DRAFT);
                processMailroom(requestId, optionalMailroom.get(), invoiceReceived, invoiceHeader, document, documentApprovalContainer, auth);
            } else {
                logger.info("Mailroom match NOT found | RequestId: {}", requestId);
                invoiceReceived.setDmrCreatedAt(DateTimeUtils.getCurrentTimestamp());
                invoiceReceived.setIsOcrDone(isOcrDone);
                invoiceReceived.setOcrStatus(ocrStatus);

                List<InvoiceItem> oldInvoiceItems = invoiceHeader.getInvoiceItems();

                documentApprovalContainer = document.getDocumentApprovalContainer();

                logger.info("Preparing Seller and Buyer Details | RequestId: {}", requestId);
                prepareSellerDetails(invoiceHeader, invoiceJson, auth.getCompanyCode());
                prepareBuyerDetails(invoiceHeader, invoiceJson, auth.getCompanyCode());

                logger.info("Mapping invoice data from JSON | DocumentId: {}", document.getId());
                InvoiceHeaderMapper.prepareInvoiceHeaderFromJson(invoiceHeader, invoiceJson, document);

                handleLookupData(invoiceHeader, invoiceJson);
                prepareDocumentFromJson(document, invoiceJson);
                InvoiceHeaderMapper.prepareInvoiceReceivedFromJson(invoiceReceived, invoiceJson, auth.getCompanyCode(), invoiceHeader);

                invoiceReceived.setGovtJson(objectMapper.writeValueAsString(invoiceJson));
                invoiceReceived.setJsonSource(JsonSource.OCR);
                documentApprovalContainer.getDocuments().add(document);

                logger.info("Setting Document Types | RequestId: {}", requestId);
                LookupData documentType = lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_INVOICE)
                        .orElseThrow(() -> {
                            logger.error("DocumentType not found in Lookup | Type: {} | Value: {}", StaticDataRegistry.LOOKUP_TYPE_DOCUMENT, StaticDataRegistry.LOOKUP_VALUE_INVOICE);
                            return new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_TYPE_NOT_FOUND);
                        });

                invoiceHeader.setDocType(documentType);
                invoiceHeader.setDocTypeId(documentType.getId());

                lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_INVOICE_TYPE, invoiceJson.getTranDtls().getSupTyp())
                        .ifPresent(type -> {
                            invoiceHeader.setInvoiceType(type);
                            invoiceHeader.setInvoiceTypeId(type.getId());
                        });

                lookupRepository.findByTypeAndValue(StaticDataRegistry.LOOKUP_INVOICE_SUB_TYPE, StaticDataRegistry.LOOKUP_INVOICE_SUB_TYPE_REGULAR)
                        .ifPresent(subType -> {
                            invoiceHeader.setInvoiceSubType(subType);
                            invoiceHeader.setInvoiceSubTypeId(subType.getId());
                        });

                logger.info("Setting report status based on OCR result | Status: {}", ocrStatus);
                setReportStatusForInvoice(documentApprovalContainer, ocrStatus);

                if (!oldInvoiceItems.isEmpty()) {
                    logger.debug("Deleting old invoice items | Count: {}", oldInvoiceItems.size());
                    invoiceItemRepository.deleteAll(oldInvoiceItems);
                }

                logger.info("Saving updated entities to database | RequestId: {}", requestId);
                reportRepository.saveAndFlush(documentApprovalContainer);
                documentRepository.saveAndFlush(document);
                invoiceHeaderRepository.save(invoiceHeader);
                invoiceReceivedRepository.save(invoiceReceived);
            }

            invoiceHeaderRepository.flush();
            invoiceReceivedRepository.flush();

            logger.info("Successfully saved Invoice OCR/GOVT JSON | RequestId: {}", requestId);
            return true;

        } catch (Exception e) {
            logger.error("Exception occurred in saveInvoiceOCRJson | RequestId: {} | Error: {}", requestId, e.getMessage(), e);
            if (null != documentApprovalContainer) {
                documentApprovalContainerUserService.markAsDraft(documentApprovalContainer);
            }
            return false;
        }
    }

    private void setReportStatusForInvoice(DocumentApprovalContainer dac, String ocrStatus) {
        switch (ocrStatus.toUpperCase()) {
            case "BUYER_NOT_FOUND":
                dac.setReportStatus(ReportStatus.FAILED);
                dac.setStatusRemarks(StaticDataRegistry.StatusRemarks.BUYER_NOT_FOUND);
                break;
            case "INVOICE_NOT_READABLE":
                dac.setReportStatus(ReportStatus.FAILED);
                dac.setStatusRemarks(StaticDataRegistry.StatusRemarks.INVOICE_NOT_READABLE);
                break;
            case "DUPLICATE_INVOICE_NO":
                dac.setReportStatus(ReportStatus.FAILED);
                dac.setStatusRemarks(StaticDataRegistry.StatusRemarks.DUPLICATE_INVOICE_NO);
                break;
            case "OCR_FAILURE":
                dac.setReportStatus(ReportStatus.FAILED);
                dac.setStatusRemarks(StaticDataRegistry.StatusRemarks.OCR_FAILED);
                break;
            default:
                dac.setReportStatus(ReportStatus.DRAFT);
        }
    }

    @Override
    @Transactional
    public boolean save(InvoiceDetailsReq invoiceDetailsRequest, IAuthContextViewModel auth) {
        long invoiceHeaderId = invoiceDetailsRequest.getInvoiceHeaderId();

        InvoiceHeader invoiceHeader = fetchInvoiceHeader(invoiceHeaderId);
        Document document = validateAndFetchDocument(invoiceHeader);
        ensureUniqueInvoiceNumber(invoiceDetailsRequest, auth, document);

        DocumentApprovalContainer documentApprovalContainer = document.getDocumentApprovalContainer();

        logger.info("Preparing document and approval container for invoiceHeaderId: {}", invoiceHeaderId);
        prepareDocumentAndApprovalContainer(document, documentApprovalContainer, invoiceDetailsRequest);

        logger.info("Updating Invoice Header for invoiceHeaderId: {}", invoiceHeaderId);
        InvoiceHeaderMapper.prepareInvoiceHeader(invoiceHeader, invoiceDetailsRequest, document);

        processLineItems(invoiceDetailsRequest, invoiceHeader, auth, document);
        addUpdateSegmentDetails(invoiceDetailsRequest.getSegmentDetails(), auth);

//        if (!invoiceHeader.getHasPrecedingDocument()) { // -- commenting as we have not using delete method from process line item in case of po based
        deleteLineItems(invoiceHeader, invoiceDetailsRequest.getLineItemIds());
//        }
        updateClaimAmounts(document, documentApprovalContainer, invoiceHeader);
        updateSellerDetails(invoiceDetailsRequest, invoiceHeader);
        updateBuyerDetails(invoiceDetailsRequest, auth, invoiceHeader);
        handleSupportingDocuments(invoiceDetailsRequest, document);

        invoiceHeader.setDynamicFieldsJson(invoiceDetailsRequest.getDynamicFieldsJson());
        persistEntities(document, documentApprovalContainer, invoiceHeader);

        return invoiceHeader.getInvoiceHeaderId() != null;
    }

    private InvoiceHeader fetchInvoiceHeader(long invoiceHeaderId) {
        return invoiceHeaderRepository.findById(invoiceHeaderId)
                .orElseThrow(() -> {
                    logger.warn("InvoiceHeader not found for id: {}", invoiceHeaderId);
                    return new RecordNotFoundException("There was an issue saving the invoice, please try again later...");
                });
    }

    private Document validateAndFetchDocument(InvoiceHeader invoiceHeader) {
        Document document = invoiceHeader.getDocument();
        if (document == null) {
            throw new IllegalArgumentException("Invoice must have an associated Document.");
        }
        return document;
    }

    private void ensureUniqueInvoiceNumber(InvoiceDetailsReq invoiceDetailsRequest, IAuthContextViewModel auth, Document document) {
        String docNo = invoiceDetailsRequest.getDocumentDetails().getDocNo();
        if (docNo == null) return;

        List<ReportStatus> excludedStatuses = List.of(
                ReportStatus.DRAFT, ReportStatus.CANCELLED, ReportStatus.DELETED, ReportStatus.DECLINED, ReportStatus.FAILED, ReportStatus.PROCESSING
        );

        boolean exists = documentRepository.existsByDocNoAndCompanyCodeAndDocumentApprovalContainer_ReportStatusNotInAndDocumentApprovalContainer_DocumentMetadata_DocumentCategoryIdAndIdNot(
                docNo, auth.getCompanyCode(), excludedStatuses,
                document.getDocumentApprovalContainer().getDocumentMetadata().getDocumentCategoryId(),
                document.getId()
        );

        if (exists) {
            throw new DuplicateRecordFoundException(String.format("Invoice number '%s' already exists", docNo));
        }
    }

    private void processLineItems(InvoiceDetailsReq invoiceDetailsRequest, InvoiceHeader invoiceHeader, IAuthContextViewModel auth, Document document) {
        addUpdateLineItemDetails(invoiceDetailsRequest.getItemList(), invoiceHeader, auth, document);

        invoiceHeader.getInvoiceItems().forEach(item ->
                item.setDynamicFieldsJson(invoiceDetailsRequest.getDynamicFieldsJson())
        );
    }

    private void handleSupportingDocuments(InvoiceDetailsReq invoiceDetailsRequest, Document document) {
        logger.info("Mapping supporting documents to Invoice document: {}", document.getId());
        documentManagementService.performMappingDocumentToSupportingDocs(invoiceDetailsRequest.getUploadedDocIds(), document.getId());
    }

    private void updateClaimAmounts(Document document, DocumentApprovalContainer documentApprovalContainer, InvoiceHeader invoiceHeader) {
        documentApprovalContainer.setApprovalContainerClaimAmount(document.getClaimAmount());
    }

    private void updateSellerDetails(InvoiceDetailsReq invoiceDetailsRequest, InvoiceHeader invoiceHeader) {
        Optional.of(invoiceDetailsRequest.getSellerDetails())
                .map(CompanyDetails::getId)
                .flatMap(companyRepository::findById)
                .ifPresent(seller -> {
                    invoiceHeader.setSellerId(seller.getCompanyId());
                    invoiceHeader.setSupplier(seller);
                });
    }

    private void updateBuyerDetails(InvoiceDetailsReq invoiceDetailsRequest, IAuthContextViewModel auth, InvoiceHeader invoiceHeader) {
        Optional.ofNullable(invoiceDetailsRequest.getBuyerDetails())
                .map(CompanyDetails::getGstin)
                .flatMap(gst -> gstinRepository.findByCompanyCodeAndGstinNo(auth.getCompanyCode(), gst))
                .ifPresentOrElse(
                        gstinDetails -> {
                            invoiceHeader.setBuyerGstinDetails(gstinDetails, stateCodeRepo);
                            logger.info("GSTIN details found and set for GSTIN: {}", gstinDetails.getGstinNo());
                            // Add any other logic you need here
                        },
                        () -> logger.info("GSTIN details not found")
                );
    }

    // Fetch seller details
    private Optional<Company> findSeller(InvoiceDetailsReq invoiceDetailsRequest) {
        return Optional.ofNullable(invoiceDetailsRequest.getSellerDetails())
                .map(CompanyDetails::getId)
                .flatMap(companyRepository::findById);
    }

    // Fetch buyer GSTIN details
    private Optional<GstinDetail> findBuyerGstin(InvoiceDetailsReq invoiceDetailsRequest, IAuthContextViewModel auth) {
        return Optional.ofNullable(invoiceDetailsRequest.getBuyerDetails())
                .map(CompanyDetails::getGstin)
                .flatMap(gst -> gstinRepository.findByCompanyCodeAndGstinNo(auth.getCompanyCode(), gst));
    }

    private void addUpdateSegmentDetails(SegmentRatioToEntityRequest segmentDetails, IAuthContextViewModel auth) {
        if (null == segmentDetails)
            return;
        logger.info("Saving segment details to Invoice.");
        ratioCategoryMasterService.saveSegmentRatio(auth.getCompanyCode(), segmentDetails);
    }

    private void persistEntities(Document document, DocumentApprovalContainer documentApprovalContainer, InvoiceHeader invoiceHeader) {
        documentRepository.saveAndFlush(document);
        reportRepository.saveAndFlush(documentApprovalContainer);
        invoiceHeaderRepository.saveAndFlush(invoiceHeader);
    }

    private InvoiceViewModel getViewModel(Document entity) {
        InvoiceViewModel viewModel = new InvoiceViewModel();
        BeanUtils.copyProperties(entity, viewModel);
//        viewModel.setExpenseSubgroupMarker(StaticDataRegistry.getExpenseSubgroupMarker(entity.getDocumentSubgroup()));
        return viewModel;
    }

    private DocumentRule getApplicableRule(Document document) {
        logger.trace("getApplicableRule: Finding the Expense with id: {}", document.getId());

        DocumentSubgroup subgroup = document.getDocumentSubgroup();

        Optional<DocumentRule> rule = subgroup.getRules().stream().filter(r -> !r.isFrozen()).filter(r -> isRuleMatch(r, document)).findFirst();

        return rule.orElseThrow(() -> new RecordNotFoundException("Could not find applicable rule for expense"));
    }

    private boolean isRuleMatch(DocumentRule rule, Document document) {
        boolean result = true;

//        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
//            result = rule.getBranchCode().equals(document.getDocumentApprovalContainer().getEmployeeBranch());
//            if (!result) return false;
//        }
//
//        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
//            result = rule.getDepartmentCode().equals(document.getDocumentApprovalContainer().getEmployeeDepartment());
//            if (!result) return false;
//        }
//
//        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
//            result = rule.getCostCenterCode().equals(document.getDocumentApprovalContainer().getEmployeeCostCenter());
//            if (!result) return false;
//        }
//
//        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
//            result = rule.getEmployeeType().equals(document.getDocumentApprovalContainer().getEmployeeType());
//            if (!result) return false;
//        }
//
//        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
//            result = rule.getEmployeeGrade().equals(document.getDocumentApprovalContainer().getEmployeeGrade());
//            if (!result) return false;
//        }
//
//        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getLocationCategory())) {
//            result = rule.getLocationCategory().equals(document.getLocationCategory());
//            if (!result) return false;
//        }

        return result;
    }

// validation subordinates


    private List<MetadataLimitRule> getApplicableRule(DocumentApprovalContainer report) {
        logger.trace("getApplicableRule: Finding the Metadata Limit Rules for Report with id: {}", report.getId());

        return metadataLimitRuleRepository.findByCompanyCodeAndDocumentMetadataId(report.getCompanyCode(), report.getDocumentMetadataId()).stream().filter(r -> isRuleMatch(r, report)).collect(Collectors.toList());
    }

    private boolean isRuleMatch(MetadataLimitRule rule, DocumentApprovalContainer report) {
        boolean result = true;

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getBranchCode())) {
            result = rule.getBranchCode().equals(report.getEmployeeBranch());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getDepartmentCode())) {
            result = rule.getDepartmentCode().equals(report.getEmployeeDepartment());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getCostCenterCode())) {
            result = rule.getCostCenterCode().equals(report.getEmployeeCostCenter());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeType())) {
            result = rule.getEmployeeType().equals(report.getEmployeeType());
            if (!result) return false;
        }

        if (StaticDataRegistry.isNotNullOrEmptyOrWhitespace(rule.getEmployeeGrade())) {
            result = rule.getEmployeeGrade().equals(report.getEmployeeGrade());
            if (!result) return false;
        }

        return result;
    }

    private boolean isParentExpenseReportSubmittable(DocumentApprovalContainer report) {
        return report.getReportStatus() == ReportStatus.DRAFT || report.getReportStatus() == ReportStatus.SENT_BACK;
    }

    private File convert(MultipartFile invoice) {
        File file = new File(invoice.getOriginalFilename());
        try (OutputStream outputStream = new FileOutputStream(file)) {
            outputStream.write(invoice.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return file;
    }

    @Override
    public void deleteLineItem(long invoiceHeaderId, List<Long> lineItemIds, IAuthContextViewModel auth) {
        Optional.ofNullable(lineItemIds).ifPresent(ids -> ids.forEach(lineItemId -> {
            InvoiceItem item = invoiceItemRepository.findById(lineItemId).orElseThrow(() -> new RecordNotFoundException(String.format("Could not find line-item with id: %d for Invoice with id: %d", lineItemId, invoiceHeaderId)));
            invoiceItemRepository.delete(item);
        }));
        invoiceItemRepository.flush();
    }

    public void deleteLineItems(InvoiceHeader invoiceHeader, List<Long> lineItemIds) {
        Optional.ofNullable(lineItemIds).ifPresent(ids ->
                ids.forEach(lineItemId -> {
                    InvoiceItem item = invoiceItemRepository.findById(lineItemId).orElseThrow(() -> new RecordNotFoundException(String.format("Could not find line-item with id: %d for Invoice with id: %d", lineItemId, invoiceHeader.getInvoiceHeaderId())));
                    invoiceItemRepository.delete(item);
                    invoiceHeader.getInvoiceItems().remove(item);
                })
        );
        invoiceItemRepository.flush();
        invoiceHeaderRepository.saveAndFlush(invoiceHeader);
    }

    @Override
    public InvoiceLineItemViewModel getByInvoiceId(Long invoiceHeaderId, IAuthContextViewModel auth) {
        InvoiceLineItemViewModel viewModel = new InvoiceLineItemViewModel();
        List<InvoiceItem> itemDetails = invoiceItemRepository.findByInvoiceHeaderInvoiceHeaderId(invoiceHeaderId);

        if (null == itemDetails || itemDetails.isEmpty()) {
            logger.info("No LineItems found for Invoice {} ", invoiceHeaderId);
            return viewModel;
        }
        viewModel.setItemList(LineItemsMapper.prepareInvoiceLineItemsData(itemDetails));
        return viewModel;
    }

    @Override
    public boolean checkExistenceByInvoiceNo(long supplierId, String invoiceNo, Long id, IAuthContextViewModel auth) {
        return invoiceHeaderRepository.getCountByDocNoAndInvoiceHeaderId(supplierId, invoiceNo, id) > 0;
    }

    private boolean checkIfValidQRCodeFound(QRResponseDTO qrResponseDTO) {
        if (qrResponseDTO == null || qrResponseDTO.getQrData() == null || qrResponseDTO.getQrData().isEmpty()) {
//        throw new DomainInvariantException("The Govt. QR code could not be found for this document.");
            logger.info("checkIfValidQRCodeFound: The Govt. QR code could not be found for this document.");
            return false;
        }

        if (qrResponseDTO.getQrData().get("0").getIrn() == null) {
//        throw new DomainInvariantException("The Govt. QR code did not contain a valid IRN.");
            logger.info("checkIfValidQRCodeFound: The Govt. QR code could not be found for this document.");
            return false;
        }
        return true;
    }

    private QRResponseDTO lookForQRCode(MultipartFile invoice, IAuthContextViewModel auth) throws IOException {
        MultiValueMap<String, HttpEntity<?>> bodyValues = new LinkedMultiValueMap<>();
        Resource resource = null;
        resource = new ByteArrayResource(invoice.getBytes()) {
            @Override
            public String getFilename() {
                return invoice.getOriginalFilename();
            }
        };
        bodyValues.add("file", new HttpEntity<>(resource));
        String qrResponseJson = webClientHelper.makeRequest(ExternalServicesIdentifier.QR_READER_SERVICE.ordinal(),
                HttpMethod.POST,
                UrlConstants.QR_READER,
                Optional.of(bodyValues),
                null,
                null,
                String.class,
                auth.getToken()).block();

        return objectMapper.readValue(qrResponseJson, QRResponseDTO.class);
    }

    private QRResponseDTO lookForQRCodeUsingSignedUrl(String fileUrl, IAuthContextViewModel auth) throws IOException {

        Map<String, List<String>> requestBody = new HashMap<>();
        requestBody.put("file_url", Collections.singletonList(fileUrl));
        logger.info("requestBody {} ", requestBody);
        try {
            String qrResponseJson = webClientHelper.makeRequest(
                    ExternalServicesIdentifier.QR_READER_SERVICE.ordinal(),
                    HttpMethod.POST,
                    UrlConstants.QR_READER_URL,
                    Optional.of(requestBody),
                    null,
                    null,
                    String.class,
                    auth.getToken()
            ).block();

            return objectMapper.readValue(qrResponseJson, QRResponseDTO.class);
        } catch (Exception e) {
            logger.warn("QR Code service call failed: {}", e.getMessage());
            return new QRResponseDTO(); // Return empty response
        }
    }

    private void prepareDocument(Document entity, InvoiceDetailsReq invoiceDetailsRequest) {
        handleDocumentAmounts(entity, invoiceDetailsRequest);
        entity.setDocumentDate(handleDocumentDates(invoiceDetailsRequest, entity.getId()));
        entity.setStartDate(entity.getDocumentDate());
        entity.setEndDate(entity.getDocumentDate());

        entity.setDocNo(
                Optional.ofNullable(invoiceDetailsRequest.getDocumentDetails())
                        .map(InvoiceDocumentDetails::getDocNo)
                        .orElse(entity.getDocNo())
        );
    }

    private void handleDocumentDates(Document entity, InvoiceDetailsReq invoiceDetailsRequest) {
        if (invoiceDetailsRequest.getDocumentDetails() == null) {
            logger.error(String.format("handleDocumentDates: The invoice for entity id: %s had NULL for document Details", entity.getId()));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_SAVE_FAILURE);
        }

        // Date handling
        LocalDate invoiceDate = null;
        String invoiceDateString = invoiceDetailsRequest.getDocumentDetails().getDocDate();

        if (invoiceDateString == null || invoiceDateString.isEmpty()) {
            logger.error(String.format("prepareDocument: The invoice for entity id: %s was provdied with an empty date: %s", entity.getId(), invoiceDate));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_SAVE_FAILURE);
        }
        try {
            invoiceDate = DateTimeUtils.parseDatetoLocalDate(invoiceDateString);
        } catch (DateTimeParseException e) {
            logger.error(String.format("prepareDocument: The provided date could not be parsed: %s", invoiceDateString));
            throw new DomainInvariantException(StaticDataRegistry.ERROR_MESSAGE_INVOICE_SAVE_FAILURE);
        }

        entity.setDocumentDate(invoiceDate);
        entity.setStartDate(invoiceDate);
        entity.setEndDate(invoiceDate);
    }

    @Override
    @Transactional
    public InvoicePoMatchResponse matchInvoiceWithPurchaseOrders(InvoicePoMatchRequest request, IAuthContextViewModel auth) {
        logger.trace("matchInvoiceWithPurchaseOrders: Starting invoice-PO matching for invoice ID: {} with PO IDs: {} for company: {}",
                request.getInvoiceId(), request.getPoIds(), auth.getCompanyCode());

        try {
            logger.info("matchInvoiceWithPurchaseOrders: Starting invoice-PO matching for invoice ID: {} with PO IDs: {} for company: {}",
                    request.getInvoiceId(), request.getPoIds(), auth.getCompanyCode());

            DocumentApprovalContainer documentApprovalContainerInvoice = reportRepository.findByCompanyCodeAndId(auth.getCompanyCode(), request.getInvoiceId())
                    .orElse(null);
            if (documentApprovalContainerInvoice == null) {
                logger.error("matchInvoiceWithPurchaseOrders: DocumentApprovalContainer for invoice ID: {} not found or does not belong to company: {}",
                        request.getInvoiceId(), auth.getCompanyCode());
                throw new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
            }

            DocumentSubgroup entity = subgroupRepository.findByCompanyCodeAndId(auth.getCompanyCode(), documentApprovalContainerInvoice.getDocumentMetadata().getDocumentSubgroups().get(0).getId())
                    .orElseThrow(() -> new RecordNotFoundException(String.format("Could not find ExpenseSubgroup with id: %d", documentApprovalContainerInvoice.getDocumentMetadataId())));

            InvoiceHeader invoiceHeader = documentApprovalContainerInvoice.getDocuments().get(0).getInvoiceHeader();
            if (invoiceHeader == null) {
                logger.error("matchInvoiceWithPurchaseOrders: InvoiceHeader for invoice ID: {} not found in DocumentApprovalContainer",
                        request.getInvoiceId());
                throw new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
            }

            InvoiceDTO invoiceDTO = InvoiceHeaderMapper.mapToInvoiceDTO(invoiceHeader);



            List<PurchaseOrderHeader> purchaseOrderHeaderList = new ArrayList<>();
            // Process each PO ID
            for (Long poId : request.getPoIds()) {
                try {

                    PurchaseOrderHeader purchaseOrderHeader = purchaseOrderHeaderRepository.findByIdAndCompanyCode(poId, auth.getCompanyCode())
                            .orElse(null);
                    if (purchaseOrderHeader == null) {
                        logger.error("matchInvoiceWithPurchaseOrders: PurchaseOrderHeader for PO ID: {} not found in company: {}",
                                poId, auth.getCompanyCode());
                        throw new RecordNotFoundException(StaticDataRegistry.ERROR_MESSAGE_RECORD_NOT_FOUND);
                    } else {
                        purchaseOrderHeaderList.add(purchaseOrderHeader);
                    }
                } catch (Exception e) {
                    logger.error("matchInvoiceWithPurchaseOrders: Error processing PO ID: {} for invoice ID: {}. Error: {}",
                            poId, request.getInvoiceId(), e.getMessage(), e);

                }
            }

            logger.debug("matchInvoiceWithPurchaseOrders: Found {} POs for invoice ID: {}", purchaseOrderHeaderList.size(), request.getInvoiceId());
        } catch (Exception e) {
            logger.error("matchInvoiceWithPurchaseOrders: Unexpected error during invoice-PO matching for invoice ID: {}. Error: {}",
                    request.getInvoiceId(), e.getMessage(), e);
            throw new RuntimeException("Failed to match invoice with purchase orders: " + e.getMessage(), e);
        }
        return null;
    }

    private boolean performInvoicePoMatching(InvoiceHeader invoiceHeader, List<PurchaseOrderItem> poItems, IAuthContextViewModel auth) {
        // Implement actual matching logic here
        // This is a placeholder implementation

        try {
            // Example matching criteria:
            // 1. Check if invoice amount matches PO amount (within tolerance)
            // 2. Check if invoice items match PO items
            // 3. Check vendor details
            // 4. Check other business rules

            logger.debug("performInvoicePoMatching: Performing matching validation for invoice ID: {} with {} PO items",
                    invoiceHeader.getInvoiceHeaderId(), poItems.size());

            // Placeholder logic - replace with actual business logic
            return true; // For now, always return true

        } catch (Exception e) {
            logger.error("performInvoicePoMatching: Error during matching validation: {}", e.getMessage(), e);
            return false;
        }
    }

    private String determineMatchType(InvoiceHeader invoiceHeader) {
        // Determine if it's 2-way or 3-way matching based on configuration
        // This is a placeholder implementation

        try {
            // Check match configuration or invoice settings
            // For now, return default
            return "2-way";

        } catch (Exception e) {
            logger.warn("determineMatchType: Error determining match type, defaulting to 2-way: {}", e.getMessage());
            return "2-way";
        }
    }

}
