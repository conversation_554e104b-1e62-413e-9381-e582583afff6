package in.taxgenie.pay_expense_pvv.utils;

public class UrlConstants {
    public static final String QR_READER = "/json_pdf_services/qr_data_extraction_from_pdf_file";

    public static final String QR_READER_URL = "/json_pdf_services/get-qr-data-from-pdf-url";

    public static final String INVOICE_OCR = "/ocr/invoice";

    public static final String DOC_MATCHER = "/api/v1/document-matcher/match";

    public static final String KEY_VALUE_ALL_PAIRS_LOOKUP = "/employees/get-key-value-pairs";

    public static final String CREATE_EXCEL_HISTORY = "/excelhistory";

    public static final String UPDATE_EXCEL_HISTORY = "/excelhistory/update-response";

    public static final String GET_SUBSCRIPTION_FOR_PRODUCT = "/sub/getSubscriptionDetailsForProduct";

    public static final String MDM_GSTIN_URL = "/v1/gstin-master/get-by-gstin";

    public static final String DEVELOPER_ATTRIBUTES = "/v1/developer-attributes?environment=sandbox";

    public static final String DEVELOPER_APP_KEYS = "/developer-app-keys";

    public static final String API4_BUSINESS_OAUTH_PROXY = "/oauth/v1/token";
    public static final String API4_BUSINESS_GENERATE_OTP = "/gov/v1/otp/generate";

    public static final String API4_BUSINESS_VERIFY_OTP = "/gov/v1/otp/verify";

    public static final String API4_BUSINESS_SYNC_DATA = "/einvoice/gov/data";
    public static final String DOC_MATCHER = "/api/v1/document-matcher/match";
}
