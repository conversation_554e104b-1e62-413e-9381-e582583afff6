package in.taxgenie.pay_expense_pvv.viewmodels.po;

import in.taxgenie.pay_expense_pvv.invoice.message.CompanyDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.PaymentDetails;
import in.taxgenie.pay_expense_pvv.invoice.message.ShipToDetails;
import in.taxgenie.pay_expense_pvv.viewmodels.budget.BudgetDetails;
import in.taxgenie.pay_expense_pvv.viewmodels.masters.SegmentRatioToEntityRequest;
import in.taxgenie.pay_expense_pvv.viewmodels.pr.AdditionalDetailsView;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderCreateViewModel {

    @Valid private PurchaseOrderDetailsViewModel poDetails;
    @Valid
    private List<PurchaseOrderItemDetailsViewModel> itemDetails;
    private BudgetDetails budgetDetails;
    private SegmentRatioToEntityRequest segmentDetails;
    private AdditionalDetailsView additionalDetails;
    private CompanyDetails vendorDetails;
    private CompanyDetails buyerDetails;
    private ShipToDetails shipToDetails;
    private PaymentDetails paymentDetails;
    private String dynamicFieldsJson;
    private Map<String, Object> businessDetails;
    private Long containerId;
    private List<Long> lineItemIds; // delete items
    private List<Long> uploadedDocIds; // map docs
    private List<Integer> segmentIds;

}
