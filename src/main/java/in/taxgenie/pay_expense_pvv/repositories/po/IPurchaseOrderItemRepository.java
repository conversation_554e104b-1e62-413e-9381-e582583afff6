package in.taxgenie.pay_expense_pvv.repositories.po;

import in.taxgenie.pay_expense_pvv.entities.po.PurchaseOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IPurchaseOrderItemRepository extends JpaRepository<PurchaseOrderItem, Long> {
    @Modifying
    @Query("DELETE FROM PurchaseOrderItem poi WHERE poi.purchaseOrderHeader.id = :purchaseOrderHeaderId")
    void deleteByPurchaseOrderHeaderId(Long purchaseOrderHeaderId);

    List<PurchaseOrderItem> findByPurchaseOrderHeaderId(long purchaseOrderId);

    @Query("SELECT poi FROM PurchaseOrderItem poi " +
           "WHERE poi.purchaseOrderHeader.document.companyCode = :companyCode " +
           "AND poi.purchaseOrderHeaderId = :purchaseOrderHeaderId")
    List<PurchaseOrderItem> findByCompanyCodeAndPurchaseOrderHeaderId(
            @Param("companyCode") Long companyCode,
            @Param("purchaseOrderHeaderId") Long purchaseOrderHeaderId);
}
