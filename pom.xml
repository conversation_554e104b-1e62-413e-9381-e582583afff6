<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.6</version>
        <relativePath/> <!-- lookup parent endPeriod repository -->
    </parent>
    <groupId>in.taxgenie</groupId>
    <artifactId>pay-document-pvv</artifactId>
    <version>3.17.79</version>
    <name>${project.name}</name>
    <description>PayExpense backend - TaxGenie</description>
    <properties>
        <java.version>17</java.version>
        <sonar.organization>personalpvv</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
        <project.build.sourceEncoding>ASCII</project.build.sourceEncoding>
        <deployment.type>jar</deployment.type>
        <project.name>pay-documentAction-pvv</project.name>
        <querydsl.version>5.0.0</querydsl.version>
    </properties>
    <packaging>${deployment.type}</packaging>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>spring-cloud-gcp-dependencies</artifactId>
                <version>3.4.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-pubsub</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-envers</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.datatype/jackson-datatype-jsr310 -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.18.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.api.grpc</groupId>
            <artifactId>grpc-google-cloud-pubsub-v1</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.28</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>2.5.5</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger2 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-boot-starter -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/io.springfox/springfox-swagger-ui -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.2</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <scope>runtime</scope>
            <version>0.11.2</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <scope>runtime</scope>
            <version>0.11.2</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-s3 -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.12.167</version>
        </dependency>

        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>spring-cloud-gcp-starter-storage</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api</groupId>
            <artifactId>gax</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.auth</groupId>
            <artifactId>google-auth-library-oauth2-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-core-http</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.codehaus.mojo/exec-maven-plugin -->
        <dependency>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/net.sf.jasperreports/jasperreports -->
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>6.19.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.lowagie</groupId>
                    <artifactId>itext</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20180130</version>
        </dependency>
        <!-- Font Extensions as many fonts not available in JDK used in Docker -->
        <dependency>
            <groupId>ar.com.fdvs</groupId>
            <artifactId>DynamicJasper-core-fonts</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.5.2</version>
        </dependency>

        <!--mapper -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.4.2.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.4.2.Final</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.taxgenie</groupId>
            <artifactId>utils</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.taxgenie.cem</groupId>
            <artifactId>cem-dtos</artifactId>
            <version>1.2.11-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <classifier>jakarta</classifier>
            <version>${querydsl.version}</version>
        </dependency>

        <!-- For Maven projects -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>3.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>7.0.2.Final</version>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <version>1.20.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf-openpdf</artifactId>
            <version>9.1.22</version>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-core</artifactId>
            <version>9.1.22</version>
        </dependency>

        <dependency>
            <groupId>com.taxgenie.libraries</groupId>
            <artifactId>pdf-service</artifactId>
            <version>1.0.3-RELEASE</version>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.querydsl</groupId>
                        <artifactId>querydsl-apt</artifactId>
                        <classifier>jakarta</classifier>
                        <version>${querydsl.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>parse-version</id>
                        <goals>
                            <goal>parse-version</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.8.1</version>
            </plugin>


            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- https://mvnrepository.com/artifact/org.sonarsource.scanner.maven/sonar-maven-plugin -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.1.2184</version>
            </plugin>
                <!-- Plugin for WAR packaging -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <configuration>
                        <!-- Set the main class startPeriod extend SpringBootServletInitializer -->
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                                <classpathPrefix>lib/</classpathPrefix>
                                <mainClass>in.taxgenie.pay_expense_pvv.PayExpensePvvApplication</mainClass>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.12</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <includes>
                    <include>META-INF/context.xml</include>
                </includes>
                <targetPath>META-INF</targetPath>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>build-war</id>
            <activation>
                <property>
                    <name>buildWar</name>
                </property>
            </activation>
            <properties>
                <deployment.type>war</deployment.type>
            </properties>
        </profile>
<!--        <profile>-->
<!--            <id>deploy-dev</id>-->
<!--            <activation>-->
<!--                <property>-->
<!--                    <name>deployDev</name>-->
<!--                </property>-->
<!--            </activation>-->
<!--            <properties>-->
<!--                <project.name>pem_dev</project.name>-->
<!--                <profile.name>dev</profile.name>-->
<!--            </properties>-->
<!--            <build>-->
<!--                <plugins>-->
<!--                    &lt;!&ndash; Rename the context-test.xml file startPeriod context.xml &ndash;&gt;-->
<!--                    <plugin>-->
<!--                        <groupId>org.apache.maven.plugins</groupId>-->
<!--                        <artifactId>maven-antrun-plugin</artifactId>-->
<!--                        <version>3.0.0</version>-->
<!--                        <executions>-->
<!--                            <execution>-->
<!--                                <id>rename-context-file</id>-->
<!--                                <phase>prepare-package</phase>-->
<!--                                <goals>-->
<!--                                    <goal>run</goal>-->
<!--                                </goals>-->
<!--                                <configuration>-->
<!--                                    <target>-->
<!--                                        <copy file="${project.basedir}/src/main/resources/__logback.xml"-->
<!--                                              tofile="${project.build.directory}/${project.build.finalName}/WEB-INF/classes/logback.xml"/>-->
<!--                                    </target>-->
<!--                                </configuration>-->
<!--                            </execution>-->
<!--                        </executions>-->
<!--                    </plugin>-->
<!--                </plugins>-->
<!--            </build>-->
<!--        </profile>-->
        <profile>
            <id>deploy-abhfl-uat</id>
            <activation>
                <property>
                    <name>deployAbhflUat</name>
                </property>
            </activation>
            <properties>
                <project.name>pem_abhfl</project.name>
                <profile.name>abhfl-uat</profile.name>
            </properties>
        </profile>
        <profile>
            <id>deploy-test</id>
            <activation>
                <property>
                    <name>deployTest</name>
                </property>
            </activation>
            <properties>
                <project.name>pem_test</project.name>
                <profile.name>test</profile.name>
            </properties>
            <build>
                <plugins>
                    <!-- Other plugins... -->

                    <!-- Copy the context-test.xml file startPeriod the META-INF directory -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>3.3.2</version>
                        <configuration>
                            <webResources>
                                <resource>
                                    <directory>${project.basedir}/src/main/webapp/META-INF</directory>
                                    <includes>
                                        <include>context-test.xml</include>
                                    </includes>
                                    <targetPath>META-INF</targetPath>
                                </resource>
                            </webResources>
                        </configuration>
                    </plugin>

                    <!-- Rename the context-test.xml file startPeriod context.xml -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>rename-context-file</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <copy file="${project.basedir}/src/main/webapp/META-INF/context-test.xml"
                                              tofile="${project.build.directory}/${project.build.finalName}/META-INF/context.xml"/>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>bump-patch</id>
            <activation>
                <property>
                    <name>bumpPatch</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>versions-maven-plugin</artifactId>
                        <version>2.8.1</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>set</goal>
                                </goals>
                                <phase>validate</phase>
                                <configuration>
                                    <newVersion>
                                        ${parsedVersion.majorVersion}.${parsedVersion.minorVersion}.${parsedVersion.nextIncrementalVersion}
                                    </newVersion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>bump-minor</id>
            <activation>
                <property>
                    <name>bumpMinor</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>versions-maven-plugin</artifactId>
                        <version>2.8.1</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>set</goal>
                                </goals>
                                <phase>validate</phase>
                                <configuration>
                                    <newVersion>${parsedVersion.majorVersion}.${parsedVersion.nextMinorVersion}.0
                                    </newVersion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>bump-major</id>
            <activation>
                <property>
                    <name>bumpMajor</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>versions-maven-plugin</artifactId>
                        <version>2.8.1</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>set</goal>
                                </goals>
                                <phase>validate</phase>
                                <configuration>
                                    <newVersion>${parsedVersion.nextMajorVersion}.0.0</newVersion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-preprod</id>
            <activation>
                <property>
                    <name>releasePreprod</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>aws-login</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>dockerize-action.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-add</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>add</argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-commit</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>commit</argument>
                                        <argument>-m</argument>
                                        <argument>Before release: ${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-tag</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>tag</argument>
                                        <argument>${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-push-origin</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>push</argument>
                                        <argument>--all</argument>
                                        <argument>origin</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>dockerize</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>build</argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:${project.version}
                                        </argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:latest
                                        </argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-versioned</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:${project.version}
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-latest</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:latest
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>deploy</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>ssh-tasks.preprod.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-uat</id>
            <activation>
                <property>
                    <name>releaseUat</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>aws-login</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>dockerize-action.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-add</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>add</argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-commit</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>commit</argument>
                                        <argument>-m</argument>
                                        <argument>Before release: ${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-tag</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>tag</argument>
                                        <argument>${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-push-origin</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>push</argument>
                                        <argument>--all</argument>
                                        <argument>origin</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>dockerize</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>build</argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:${project.version}
                                        </argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:latest
                                        </argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-versioned</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:${project.version}
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-latest</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:latest
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>deploy</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>ssh-tasks.uat.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-production</id>
            <activation>
                <property>
                    <name>releaseProduction</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>aws-login</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>dockerize-action.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-add</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>add</argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-commit</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>commit</argument>
                                        <argument>-m</argument>
                                        <argument>Before release: ${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-tag</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>tag</argument>
                                        <argument>${project.version}</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>git-push-origin</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>push</argument>
                                        <argument>--all</argument>
                                        <argument>origin</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>dockerize</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>build</argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:${project.version}
                                        </argument>
                                        <argument>--tag</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:latest
                                        </argument>
                                        <argument>.</argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-versioned</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:${project.version}
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>push-latest</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>image</argument>
                                        <argument>push</argument>
                                        <argument>
                                            561014279291.dkr.ecr.ap-south-1.amazonaws.com/pay-documentAction-backend:latest
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>

                            <execution>
                                <id>deploy</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>bash</executable>
                                    <arguments>
                                        <argument>ssh-tasks.prod.sh</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
